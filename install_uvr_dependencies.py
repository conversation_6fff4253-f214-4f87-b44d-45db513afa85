#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UVR依赖安装脚本
确保所有必要的包都正确安装
"""

import subprocess
import sys
import os
import time

def run_command(cmd, description, timeout=300):
    """运行命令并显示结果"""
    print(f"\n📦 {description}...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
        elapsed = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ {description} 成功 (耗时: {elapsed:.1f}秒)")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} 失败")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} 超时")
        return False
    except Exception as e:
        print(f"❌ {description} 异常: {e}")
        return False

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("⚠️ 建议使用Python 3.8或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def upgrade_pip():
    """升级pip"""
    return run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], "升级pip")

def install_basic_dependencies():
    """安装基础依赖"""
    basic_packages = [
        "numpy",
        "scipy", 
        "librosa",
        "soundfile",
        "torch",
        "torchaudio"
    ]
    
    success_count = 0
    for package in basic_packages:
        if run_command([sys.executable, "-m", "pip", "install", package], f"安装 {package}"):
            success_count += 1
    
    print(f"\n📊 基础依赖安装结果: {success_count}/{len(basic_packages)} 成功")
    return success_count == len(basic_packages)

def install_audio_separator():
    """安装audio-separator（推荐方案）"""
    print("\n🎯 安装 audio-separator (推荐的UVR替代方案)...")
    
    # 先尝试直接安装
    if run_command([sys.executable, "-m", "pip", "install", "audio-separator"], "安装 audio-separator"):
        return True
    
    # 如果失败，尝试从GitHub安装
    print("   尝试从GitHub安装...")
    github_cmd = [sys.executable, "-m", "pip", "install", 
                  "git+https://github.com/karaokenerds/python-audio-separator.git"]
    
    if run_command(github_cmd, "从GitHub安装 audio-separator"):
        return True
    
    print("❌ audio-separator 安装失败")
    return False

def install_demucs():
    """安装demucs（备选方案）"""
    print("\n🧠 安装 demucs (备选方案)...")
    return run_command([sys.executable, "-m", "pip", "install", "demucs"], "安装 demucs")

def test_installations():
    """测试安装结果"""
    print("\n🧪 测试安装结果...")
    
    test_results = {}
    
    # 测试audio-separator
    try:
        result = subprocess.run([sys.executable, "-c", "from audio_separator.separator import Separator; print('audio-separator OK')"], 
                              capture_output=True, text=True, timeout=10)
        test_results['audio-separator'] = result.returncode == 0
        if result.returncode == 0:
            print("✅ audio-separator 导入成功")
        else:
            print("❌ audio-separator 导入失败")
    except Exception as e:
        print(f"❌ audio-separator 测试异常: {e}")
        test_results['audio-separator'] = False
    
    # 测试demucs
    try:
        result = subprocess.run([sys.executable, "-c", "import demucs; print('demucs OK')"], 
                              capture_output=True, text=True, timeout=10)
        test_results['demucs'] = result.returncode == 0
        if result.returncode == 0:
            print("✅ demucs 导入成功")
        else:
            print("❌ demucs 导入失败")
    except Exception as e:
        print(f"❌ demucs 测试异常: {e}")
        test_results['demucs'] = False
    
    # 测试基础库
    basic_libs = ['numpy', 'scipy', 'librosa', 'soundfile', 'torch']
    for lib in basic_libs:
        try:
            result = subprocess.run([sys.executable, "-c", f"import {lib}; print('{lib} OK')"], 
                                  capture_output=True, text=True, timeout=5)
            test_results[lib] = result.returncode == 0
            if result.returncode == 0:
                print(f"✅ {lib} 导入成功")
            else:
                print(f"❌ {lib} 导入失败")
        except Exception as e:
            print(f"❌ {lib} 测试异常: {e}")
            test_results[lib] = False
    
    return test_results

def create_test_audio():
    """创建测试音频文件"""
    print("\n🎵 创建测试音频文件...")
    
    try:
        import numpy as np
        import soundfile as sf
        
        # 创建简单的测试音频（5秒，44.1kHz）
        duration = 5
        sample_rate = 44100
        t = np.linspace(0, duration, duration * sample_rate, False)
        
        # 创建简单的正弦波混合
        frequency1 = 440  # A4音符
        frequency2 = 880  # A5音符
        audio = 0.3 * np.sin(2 * np.pi * frequency1 * t) + 0.2 * np.sin(2 * np.pi * frequency2 * t)
        
        # 添加一些噪声模拟复杂音频
        noise = 0.1 * np.random.normal(0, 1, len(audio))
        audio = audio + noise
        
        # 保存为立体声
        stereo_audio = np.column_stack([audio, audio])
        
        test_file = "test_audio.wav"
        sf.write(test_file, stereo_audio, sample_rate)
        
        print(f"✅ 测试音频文件已创建: {test_file}")
        print(f"   时长: {duration}秒")
        print(f"   采样率: {sample_rate}Hz")
        print(f"   文件大小: {os.path.getsize(test_file)/1024:.1f}KB")
        
        return test_file
        
    except Exception as e:
        print(f"❌ 创建测试音频失败: {e}")
        return None

def main():
    """主安装流程"""
    print("🚀 UVR依赖安装脚本")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        print("⚠️ Python版本可能不兼容，但继续尝试安装...")
    
    # 升级pip
    upgrade_pip()
    
    # 安装基础依赖
    print("\n" + "=" * 60)
    print("📦 安装基础依赖...")
    install_basic_dependencies()
    
    # 安装音频分离工具
    print("\n" + "=" * 60)
    print("🎵 安装音频分离工具...")
    
    audio_separator_ok = install_audio_separator()
    demucs_ok = install_demucs()
    
    # 测试安装结果
    print("\n" + "=" * 60)
    test_results = test_installations()
    
    # 创建测试音频
    test_audio = create_test_audio()
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 安装总结:")
    print("=" * 60)
    
    success_count = sum(1 for success in test_results.values() if success)
    total_count = len(test_results)
    
    print(f"成功安装: {success_count}/{total_count} 个包")
    
    for package, success in test_results.items():
        status = "✅" if success else "❌"
        print(f"  {status} {package}")
    
    # 给出建议
    print("\n💡 下一步建议:")
    
    if test_results.get('audio-separator', False):
        print("✅ 推荐使用 audio-separator (UVR质量)")
        print("   运行: python test_uvr_simple.py test_audio.wav")
    elif test_results.get('demucs', False):
        print("✅ 可以使用 demucs (Facebook AI)")
        print("   运行: python test_uvr_simple.py test_audio.wav")
    else:
        print("❌ 需要手动解决依赖问题")
        print("   尝试: pip install audio-separator --force-reinstall")
    
    if test_audio:
        print(f"\n🎵 测试音频已准备: {test_audio}")
        print("   可以用这个文件测试人声分离功能")

if __name__ == "__main__":
    main()
