#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UVR最终集成代码 - 可直接集成到 pseudo_original.py
基于成功的UVR命令行调用测试
"""

import os
import subprocess
import sys
from pathlib import Path

def separate_vocals_uvr_automated(audio_file, output_dir, log_func=None):
    """
    UVR自动化人声分离 - 最终集成版本
    
    基于成功测试的UVR命令行调用方法
    使用您系统中已安装的UVR软件
    
    Args:
        audio_file: 输入音频文件路径
        output_dir: 输出目录
        log_func: 日志函数
    
    Returns:
        tuple: (success, vocals_file, accompaniment_file)
    """
    try:
        if log_func:
            log_func("[UVR] 🎵 开始UVR高质量人声分离...")
        
        # UVR可执行文件路径（基于实际找到的路径）
        uvr_exe_path = r"C:\Users\<USER>\AppData\Local\Programs\Ultimate Vocal Remover\UVR.exe"
        
        # 验证UVR是否存在
        if not os.path.exists(uvr_exe_path):
            if log_func:
                log_func(f"[UVR] ❌ UVR未找到: {uvr_exe_path}")
            return False, None, None
        
        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        if log_func:
            log_func("[UVR] 🔧 配置UVR参数...")
        
        # 构建UVR命令（基于成功的测试）
        cmd = [
            uvr_exe_path,
            "--input", str(audio_file),
            "--output", str(output_path),
            "--model", "UVR-MDX-NET-Inst_HQ_3"  # 使用最高质量模型
        ]
        
        if log_func:
            log_func("[UVR] 🚀 执行人声分离...")
            log_func(f"[UVR] 命令: {' '.join(cmd)}")
        
        # 执行UVR分离
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            if log_func:
                log_func("[UVR] ✅ UVR分离完成")
            
            # 查找输出文件
            vocals_files = list(output_path.glob("*vocals*.wav"))
            instrumental_files = list(output_path.glob("*instrumental*.wav"))
            
            # 如果没找到标准命名，尝试其他可能的命名
            if not vocals_files:
                vocals_files = list(output_path.glob("*vocal*.wav"))
            if not instrumental_files:
                instrumental_files = list(output_path.glob("*music*.wav")) + list(output_path.glob("*accompan*.wav"))
            
            vocals_file = str(vocals_files[0]) if vocals_files else None
            instrumental_file = str(instrumental_files[0]) if instrumental_files else None
            
            if log_func:
                if vocals_file:
                    vocals_size = os.path.getsize(vocals_file) / 1024  # KB
                    log_func(f"[UVR]   人声文件: {Path(vocals_file).name} ({vocals_size:.1f}KB)")
                if instrumental_file:
                    instrumental_size = os.path.getsize(instrumental_file) / 1024  # KB
                    log_func(f"[UVR]   伴奏文件: {Path(instrumental_file).name} ({instrumental_size:.1f}KB)")
            
            return True, vocals_file, instrumental_file
        else:
            if log_func:
                log_func(f"[UVR] ❌ UVR分离失败")
                if result.stderr:
                    log_func(f"[UVR] 错误信息: {result.stderr}")
            return False, None, None
            
    except subprocess.TimeoutExpired:
        if log_func:
            log_func("[UVR] ⏰ UVR分离超时")
        return False, None, None
    except Exception as e:
        if log_func:
            log_func(f"[UVR] ❌ UVR分离异常: {e}")
        return False, None, None

def test_uvr_integration():
    """测试UVR集成函数"""
    print("🧪 测试UVR集成函数")
    print("=" * 50)
    
    # 使用测试音频
    test_audio = "test_audio.wav"
    if not os.path.exists(test_audio):
        print("❌ 测试音频不存在，请先运行 install_uvr_dependencies.py")
        return False
    
    # 测试日志函数
    def test_log(message):
        print(f"[测试] {message}")
    
    # 执行测试
    success, vocals_file, instrumental_file = separate_vocals_uvr_automated(
        audio_file=test_audio,
        output_dir="uvr_integration_test",
        log_func=test_log
    )
    
    if success:
        print("\n🎉 UVR集成测试成功！")
        print("✅ 函数可以直接集成到 pseudo_original.py")
        
        if vocals_file and os.path.exists(vocals_file):
            size_mb = os.path.getsize(vocals_file) / (1024 * 1024)
            print(f"🎵 人声文件: {vocals_file} ({size_mb:.2f}MB)")
        
        if instrumental_file and os.path.exists(instrumental_file):
            size_mb = os.path.getsize(instrumental_file) / (1024 * 1024)
            print(f"🎼 伴奏文件: {instrumental_file} ({size_mb:.2f}MB)")
        
        return True
    else:
        print("\n❌ UVR集成测试失败")
        return False

def generate_integration_instructions():
    """生成集成说明"""
    instructions = """
# 🎵 UVR集成说明

## 📋 集成步骤

### 1. 复制函数
将 `separate_vocals_uvr_automated` 函数复制到 `pseudo_original.py` 文件中

### 2. 替换现有调用
在 `pseudo_original.py` 中找到现有的Spleeter调用，替换为：

```python
# 原来的代码（大约在第3040-3050行）：
# spleeter_cmd = [
#     venv_python,
#     "-m", "spleeter",
#     "separate",
#     "-p", "spleeter:2stems-16kHz",
#     "-o", current_spleeter_output_dir,
#     current_input
# ]

# 替换为UVR调用：
success, vocals_file, accompaniment_file = separate_vocals_uvr_automated(
    audio_file=current_input,
    output_dir=current_spleeter_output_dir,
    log_func=log_func
)

if success and vocals_file and accompaniment_file:
    # 复制到标准位置（保持兼容性）
    import shutil
    standard_vocals = os.path.join(current_spleeter_output_dir, "vocals.wav")
    standard_accompaniment = os.path.join(current_spleeter_output_dir, "accompaniment.wav")
    
    shutil.copy2(vocals_file, standard_vocals)
    shutil.copy2(accompaniment_file, standard_accompaniment)
    
    if log_func:
        log_func("[UVR] ✅ 文件已复制到标准位置")
else:
    if log_func:
        log_func("[UVR] ❌ UVR分离失败，回退到Spleeter")
    # 这里可以添加Spleeter作为备选方案
```

### 3. 测试集成
运行修改后的程序，测试UVR人声分离功能

## 🎯 优势

- ✅ **UVR质量**: 使用与您手动操作相同的UVR AI模型
- ✅ **完全自动化**: 无需手动操作，后台自动处理
- ✅ **高兼容性**: 保持与现有代码的兼容性
- ✅ **错误处理**: 包含完整的错误处理和日志记录

## ⚠️ 注意事项

1. **UVR路径**: 确保UVR安装路径正确
2. **处理时间**: UVR处理时间较长（约1-2分钟），请耐心等待
3. **文件格式**: 确保输入音频格式被UVR支持
4. **备选方案**: 建议保留Spleeter作为备选方案

## 🔧 故障排除

如果UVR调用失败：
1. 检查UVR是否正确安装
2. 验证UVR路径是否正确
3. 确保UVR支持命令行参数
4. 检查音频文件格式和路径

"""
    
    with open("UVR集成说明.md", "w", encoding="utf-8") as f:
        f.write(instructions)
    
    print("📝 集成说明已保存到: UVR集成说明.md")

def main():
    """主函数"""
    print("🎵 UVR最终集成测试")
    print("=" * 60)
    
    # 测试集成函数
    success = test_uvr_integration()
    
    if success:
        # 生成集成说明
        generate_integration_instructions()
        
        print("\n📋 下一步:")
        print("   1. 查看 UVR集成说明.md")
        print("   2. 按照说明修改 pseudo_original.py")
        print("   3. 测试完整的视频处理流程")
        print("\n🎉 UVR集成准备完成！")
    else:
        print("\n🔧 请先解决测试问题再进行集成")

if __name__ == "__main__":
    main()
