#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版Spleeter测试 - 优化现有Spleeter配置
由于网络问题无法安装UVR相关包，我们优化现有的Spleeter
"""

import os
import sys
import time
import subprocess
from pathlib import Path
import tempfile

class EnhancedSpleeterSeparator:
    """增强版Spleeter分离器"""
    
    def __init__(self):
        self.temp_dir = Path(tempfile.mkdtemp(prefix="spleeter_enhanced_"))
        self.python_path = sys.executable
        
        print("🎵 增强版Spleeter分离器初始化")
        print(f"临时目录: {self.temp_dir}")
        
    def check_spleeter_availability(self):
        """检查Spleeter可用性"""
        print("\n🔍 检查Spleeter可用性...")
        
        try:
            # 检查是否可以导入Spleeter
            result = subprocess.run([
                self.python_path, "-c",
                "from spleeter.separator import Separator; print('Spleeter OK')"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ Spleeter可用")
                return True
            else:
                print(f"❌ Spleeter导入失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Spleeter检查异常: {e}")
            return False
    
    def method_1_spleeter_2stems_optimized(self, audio_file, output_dir):
        """方法1: 优化的Spleeter 2stems"""
        print("\n🎯 方法1: 优化Spleeter 2stems")
        
        try:
            from spleeter.separator import Separator
            import librosa
            import soundfile as sf
            import numpy as np
            
            output_path = Path(output_dir) / "spleeter_2stems_optimized"
            output_path.mkdir(parents=True, exist_ok=True)
            
            start_time = time.time()
            
            # 创建分离器
            separator = Separator('spleeter:2stems-16kHz')
            
            print("   加载音频...")
            # 使用更高质量的音频加载
            audio, sr = librosa.load(audio_file, sr=44100, mono=False)
            
            # 确保是立体声
            if audio.ndim == 1:
                audio = np.stack([audio, audio])
            elif audio.shape[0] > 2:
                audio = audio[:2]  # 只取前两个声道
            
            print("   执行分离...")
            # 转换为Spleeter期望的格式 (time, channels)
            audio_for_spleeter = audio.T
            
            # 执行分离
            prediction = separator.separate(audio_for_spleeter)
            
            # 保存结果
            vocals_path = output_path / "vocals.wav"
            accompaniment_path = output_path / "accompaniment.wav"
            
            sf.write(vocals_path, prediction['vocals'], 44100)
            sf.write(accompaniment_path, prediction['accompaniment'], 44100)
            
            elapsed = time.time() - start_time
            
            print(f"   ✅ 分离完成，耗时: {elapsed:.2f}秒")
            print(f"   人声: {vocals_path}")
            print(f"   伴奏: {accompaniment_path}")
            
            # 检查文件大小
            if vocals_path.exists():
                size_mb = vocals_path.stat().st_size / (1024 * 1024)
                print(f"   人声文件大小: {size_mb:.2f} MB")
            
            return True, elapsed, str(vocals_path), str(accompaniment_path)
            
        except Exception as e:
            print(f"   ❌ 分离失败: {e}")
            import traceback
            traceback.print_exc()
            return False, 0, None, None
    
    def method_2_spleeter_5stems(self, audio_file, output_dir):
        """方法2: Spleeter 5stems（更精细分离）"""
        print("\n🎵 方法2: Spleeter 5stems")
        
        try:
            from spleeter.separator import Separator
            import librosa
            import soundfile as sf
            import numpy as np
            
            output_path = Path(output_dir) / "spleeter_5stems"
            output_path.mkdir(parents=True, exist_ok=True)
            
            start_time = time.time()
            
            # 创建5stems分离器
            separator = Separator('spleeter:5stems-16kHz')
            
            print("   加载音频...")
            audio, sr = librosa.load(audio_file, sr=44100, mono=False)
            
            if audio.ndim == 1:
                audio = np.stack([audio, audio])
            elif audio.shape[0] > 2:
                audio = audio[:2]
            
            print("   执行5stems分离...")
            audio_for_spleeter = audio.T
            prediction = separator.separate(audio_for_spleeter)
            
            # 保存所有轨道
            for stem_name, stem_audio in prediction.items():
                stem_path = output_path / f"{stem_name}.wav"
                sf.write(stem_path, stem_audio, 44100)
                print(f"   {stem_name}: {stem_path}")
            
            elapsed = time.time() - start_time
            print(f"   ✅ 5stems分离完成，耗时: {elapsed:.2f}秒")
            
            # 返回人声和伴奏文件
            vocals_path = output_path / "vocals.wav"
            # 对于5stems，伴奏 = 其他 + 鼓 + 贝斯 + 钢琴
            return True, elapsed, str(vocals_path), None
            
        except Exception as e:
            print(f"   ❌ 5stems分离失败: {e}")
            return False, 0, None, None
    
    def method_3_spleeter_command_line(self, audio_file, output_dir):
        """方法3: Spleeter命令行模式（最稳定）"""
        print("\n🖥️ 方法3: Spleeter命令行")
        
        try:
            output_path = Path(output_dir) / "spleeter_cli"
            output_path.mkdir(parents=True, exist_ok=True)
            
            start_time = time.time()
            
            # 使用命令行调用Spleeter
            cmd = [
                self.python_path, "-m", "spleeter",
                "separate",
                "-p", "spleeter:2stems-16kHz",
                "-o", str(output_path),
                str(audio_file)
            ]
            
            print(f"   执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            elapsed = time.time() - start_time
            
            if result.returncode == 0:
                print(f"   ✅ 命令行分离成功，耗时: {elapsed:.2f}秒")
                
                # 查找输出文件
                audio_name = Path(audio_file).stem
                vocals_file = output_path / audio_name / "vocals.wav"
                accompaniment_file = output_path / audio_name / "accompaniment.wav"
                
                if vocals_file.exists():
                    print(f"   人声: {vocals_file}")
                    size_mb = vocals_file.stat().st_size / (1024 * 1024)
                    print(f"   人声文件大小: {size_mb:.2f} MB")
                
                if accompaniment_file.exists():
                    print(f"   伴奏: {accompaniment_file}")
                    size_mb = accompaniment_file.stat().st_size / (1024 * 1024)
                    print(f"   伴奏文件大小: {size_mb:.2f} MB")
                
                return True, elapsed, str(vocals_file), str(accompaniment_file)
            else:
                print(f"   ❌ 命令行分离失败: {result.stderr}")
                return False, 0, None, None
                
        except Exception as e:
            print(f"   ❌ 命令行分离异常: {e}")
            return False, 0, None, None
    
    def method_4_multi_pass_separation(self, audio_file, output_dir):
        """方法4: 多次分离（提高质量）"""
        print("\n🔄 方法4: 多次分离增强")
        
        try:
            from spleeter.separator import Separator
            import librosa
            import soundfile as sf
            import numpy as np
            
            output_path = Path(output_dir) / "spleeter_multi_pass"
            output_path.mkdir(parents=True, exist_ok=True)
            
            start_time = time.time()
            
            # 第一次分离
            print("   第1次分离...")
            separator = Separator('spleeter:2stems-16kHz')
            
            audio, sr = librosa.load(audio_file, sr=44100, mono=False)
            if audio.ndim == 1:
                audio = np.stack([audio, audio])
            
            prediction1 = separator.separate(audio.T)
            
            # 保存第一次结果
            first_vocals = prediction1['vocals']
            sf.write(output_path / "pass1_vocals.wav", first_vocals, 44100)
            
            # 第二次分离（对人声再次分离）
            print("   第2次分离（精细化）...")
            prediction2 = separator.separate(first_vocals)
            
            # 最终结果
            final_vocals = prediction2['vocals']
            final_accompaniment = prediction1['accompaniment']
            
            vocals_path = output_path / "vocals_enhanced.wav"
            accompaniment_path = output_path / "accompaniment.wav"
            
            sf.write(vocals_path, final_vocals, 44100)
            sf.write(accompaniment_path, final_accompaniment, 44100)
            
            elapsed = time.time() - start_time
            
            print(f"   ✅ 多次分离完成，耗时: {elapsed:.2f}秒")
            print(f"   增强人声: {vocals_path}")
            print(f"   伴奏: {accompaniment_path}")
            
            return True, elapsed, str(vocals_path), str(accompaniment_path)
            
        except Exception as e:
            print(f"   ❌ 多次分离失败: {e}")
            return False, 0, None, None
    
    def test_all_methods(self, audio_file, output_dir="spleeter_enhanced_test"):
        """测试所有增强方法"""
        print("🚀 开始增强版Spleeter测试")
        print("=" * 80)
        
        if not os.path.exists(audio_file):
            print(f"❌ 音频文件不存在: {audio_file}")
            return False
        
        # 检查Spleeter可用性
        if not self.check_spleeter_availability():
            print("❌ Spleeter不可用，无法进行测试")
            return False
        
        output_base = Path(output_dir)
        output_base.mkdir(exist_ok=True)
        
        results = {}
        
        # 测试所有方法
        methods = [
            ("优化2stems", self.method_1_spleeter_2stems_optimized),
            ("5stems精细", self.method_2_spleeter_5stems),
            ("命令行模式", self.method_3_spleeter_command_line),
            ("多次分离", self.method_4_multi_pass_separation)
        ]
        
        successful_method = None
        best_result = None
        
        for method_name, method_func in methods:
            try:
                success, elapsed, vocals_file, accompaniment_file = method_func(audio_file, output_base)
                results[method_name] = {
                    'success': success,
                    'time': elapsed,
                    'vocals': vocals_file,
                    'accompaniment': accompaniment_file
                }
                
                if success and not successful_method:
                    successful_method = method_name
                    best_result = results[method_name]
                    print(f"🎉 找到可用方法: {method_name}")
                    
            except Exception as e:
                print(f"❌ {method_name} 测试异常: {e}")
                results[method_name] = {
                    'success': False,
                    'time': 0,
                    'vocals': None,
                    'accompaniment': None
                }
        
        # 输出测试结果
        print("\n" + "=" * 80)
        print("📊 测试结果汇总:")
        print("=" * 80)
        
        for method_name, result in results.items():
            status = "✅ 成功" if result['success'] else "❌ 失败"
            time_str = f"{result['time']:.2f}秒" if result['time'] > 0 else "N/A"
            print(f"{method_name:15} | {status:8} | 耗时: {time_str}")
        
        if successful_method:
            print(f"\n🎯 推荐使用: {successful_method}")
            print(f"📁 输出目录: {output_base}")
            
            # 生成集成代码
            self.create_integration_code(successful_method, best_result)
            return True
        else:
            print("\n❌ 所有方法都失败了")
            return False
    
    def create_integration_code(self, method_name, result):
        """生成集成代码"""
        print(f"\n📝 生成集成代码 (基于 {method_name})...")
        
        if "2stems" in method_name or "命令行" in method_name:
            code = '''
def separate_vocals_enhanced_spleeter(audio_file, output_dir, log_func=None):
    """
    增强版Spleeter人声分离 - 集成版本
    
    Args:
        audio_file: 输入音频文件路径
        output_dir: 输出目录
        log_func: 日志函数
    
    Returns:
        tuple: (success, vocals_file, accompaniment_file)
    """
    try:
        from spleeter.separator import Separator
        import librosa
        import soundfile as sf
        import numpy as np
        from pathlib import Path
        
        if log_func:
            log_func("[增强Spleeter] 开始高质量人声分离...")
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 创建分离器
        separator = Separator('spleeter:2stems-16kHz')
        
        if log_func:
            log_func("[增强Spleeter] 加载音频...")
        
        # 使用更高质量的音频加载
        audio, sr = librosa.load(audio_file, sr=44100, mono=False)
        
        # 确保是立体声
        if audio.ndim == 1:
            audio = np.stack([audio, audio])
        elif audio.shape[0] > 2:
            audio = audio[:2]
        
        if log_func:
            log_func("[增强Spleeter] 执行人声分离...")
        
        # 执行分离
        prediction = separator.separate(audio.T)
        
        # 保存结果
        vocals_path = output_path / "vocals.wav"
        accompaniment_path = output_path / "accompaniment.wav"
        
        sf.write(vocals_path, prediction['vocals'], 44100)
        sf.write(accompaniment_path, prediction['accompaniment'], 44100)
        
        if log_func:
            log_func(f"[增强Spleeter] ✅ 分离完成")
            log_func(f"[增强Spleeter]   人声: {vocals_path.name}")
            log_func(f"[增强Spleeter]   伴奏: {accompaniment_path.name}")
        
        return True, str(vocals_path), str(accompaniment_path)
        
    except Exception as e:
        if log_func:
            log_func(f"[增强Spleeter] ❌ 分离失败: {e}")
        return False, None, None
'''
        
        # 保存集成代码
        with open("spleeter_enhanced_integration.py", "w", encoding="utf-8") as f:
            f.write(code)
        
        print(f"✅ 集成代码已保存到: spleeter_enhanced_integration.py")
    
    def cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
            print(f"🧹 清理临时目录: {self.temp_dir}")
        except Exception as e:
            print(f"⚠️ 清理临时目录失败: {e}")

def main():
    """主测试函数"""
    print("🚀 增强版Spleeter集成测试")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        # 使用自动生成的测试音频
        test_audio = "test_audio.wav"
        if not os.path.exists(test_audio):
            print("❌ 请先运行 install_uvr_dependencies.py 生成测试音频")
            print("或者指定音频文件: python test_spleeter_enhanced.py <音频文件>")
            return
        audio_file = test_audio
    else:
        audio_file = sys.argv[1]
    
    if not os.path.exists(audio_file):
        print(f"❌ 音频文件不存在: {audio_file}")
        return
    
    # 创建测试器
    separator = EnhancedSpleeterSeparator()
    
    try:
        # 执行测试
        success = separator.test_all_methods(audio_file)
        
        if success:
            print("\n🎉 增强版Spleeter测试成功！")
            print("✅ 可以集成到正式代码中")
            print("📋 下一步:")
            print("   1. 查看生成的 spleeter_enhanced_integration.py")
            print("   2. 将函数集成到 pseudo_original.py")
            print("   3. 替换现有的Spleeter调用")
        else:
            print("\n❌ 增强版Spleeter测试失败")
            print("🔧 请检查Spleeter安装")
            
    finally:
        # 清理资源
        separator.cleanup()

if __name__ == "__main__":
    main()
