#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UVR自动化测试 - 通过文件监控方式
"""

import os
import time
import shutil
import subprocess
from pathlib import Path

def test_uvr_automation():
    """测试UVR自动化方案"""
    print("🎵 UVR自动化测试")
    print("=" * 50)
    
    # 文件路径
    input_file = r"E:\视频处理\人声分离测试\原始音频.wav"
    output_dir = r"E:\视频处理\人声分离测试"
    uvr_exe = r"C:\Users\<USER>\AppData\Local\Programs\Ultimate Vocal Remover\UVR.exe"
    
    # 检查文件
    if not os.path.exists(input_file):
        print(f"❌ 输入文件不存在: {input_file}")
        return False
    
    if not os.path.exists(uvr_exe):
        print(f"❌ UVR不存在: {uvr_exe}")
        return False
    
    print(f"✅ 输入文件: {input_file}")
    file_size = os.path.getsize(input_file) / (1024 * 1024)
    print(f"📊 文件大小: {file_size:.2f}MB")
    
    # 记录开始时的文件列表
    initial_files = set()
    for file in os.listdir(output_dir):
        if file.endswith('.wav'):
            initial_files.add(file)
    
    print(f"📁 初始文件数量: {len(initial_files)}")
    
    # 方法1: 尝试通过拖拽模拟
    print("\n🎯 方法1: 模拟拖拽启动UVR")
    
    try:
        # 启动UVR并传递文件路径
        cmd = [uvr_exe, input_file]
        print(f"执行命令: {' '.join(cmd)}")
        
        # 启动UVR（非阻塞）
        process = subprocess.Popen(cmd, 
                                 creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)
        
        print(f"✅ UVR已启动，进程ID: {process.pid}")
        print("⏳ 等待UVR处理文件...")
        print("💡 请在UVR界面中点击'Start Processing'开始处理")
        print("🔍 监控输出目录变化...")
        
        # 监控文件变化
        max_wait_time = 600  # 10分钟
        check_interval = 5   # 每5秒检查一次
        elapsed_time = 0
        
        while elapsed_time < max_wait_time:
            time.sleep(check_interval)
            elapsed_time += check_interval
            
            # 检查新文件
            current_files = set()
            for file in os.listdir(output_dir):
                if file.endswith('.wav'):
                    current_files.add(file)
            
            new_files = current_files - initial_files
            
            if new_files:
                print(f"\n🎉 检测到新文件! ({elapsed_time}秒后)")
                for new_file in new_files:
                    file_path = os.path.join(output_dir, new_file)
                    size_mb = os.path.getsize(file_path) / (1024 * 1024)
                    print(f"📄 {new_file}: {size_mb:.2f}MB")
                
                print("✅ UVR处理完成!")
                return True
            
            if elapsed_time % 30 == 0:  # 每30秒显示一次进度
                print(f"⏳ 已等待 {elapsed_time}秒...")
        
        print(f"\n⏰ 等待超时 ({max_wait_time}秒)")
        print("❌ 未检测到新的输出文件")
        
        # 尝试终止进程
        try:
            process.terminate()
            print("🛑 UVR进程已终止")
        except:
            pass
        
        return False
        
    except Exception as e:
        print(f"❌ 启动UVR失败: {e}")
        return False

def manual_uvr_test():
    """手动UVR测试指导"""
    print("\n" + "=" * 50)
    print("📋 手动UVR测试指导")
    print("=" * 50)
    
    input_file = r"E:\视频处理\人声分离测试\原始音频.wav"
    output_dir = r"E:\视频处理\人声分离测试"
    
    print("请按照以下步骤手动测试UVR:")
    print(f"1. 打开UVR软件")
    print(f"2. 将音频文件拖入UVR: {input_file}")
    print(f"3. 设置输出目录为: {output_dir}")
    print(f"4. 选择模型: UVR-MDX-NET-Inst_HQ_3")
    print(f"5. 点击'Start Processing'")
    print(f"6. 等待处理完成")
    print(f"7. 检查输出文件")
    
    input("\n按Enter键继续检查结果...")
    
    # 检查输出文件
    print("\n🔍 检查输出文件...")
    
    wav_files = []
    for file in os.listdir(output_dir):
        if file.endswith('.wav') and file != '原始音频.wav':
            wav_files.append(file)
    
    if wav_files:
        print(f"✅ 找到 {len(wav_files)} 个输出文件:")
        for file in wav_files:
            file_path = os.path.join(output_dir, file)
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            mod_time = os.path.getmtime(file_path)
            mod_time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(mod_time))
            print(f"📄 {file}: {size_mb:.2f}MB (修改时间: {mod_time_str})")
        
        print("\n🎉 UVR手动测试成功!")
        return True
    else:
        print("❌ 未找到输出文件")
        return False

def main():
    """主函数"""
    print("🚀 UVR自动化测试")
    
    # 首先尝试自动化方法
    success = test_uvr_automation()
    
    if not success:
        print("\n💡 自动化方法失败，尝试手动指导...")
        success = manual_uvr_test()
    
    if success:
        print("\n🎯 下一步:")
        print("1. 确认UVR输出文件质量")
        print("2. 记录UVR的工作流程")
        print("3. 设计自动化集成方案")
    else:
        print("\n🔧 建议:")
        print("1. 确认UVR软件正常工作")
        print("2. 手动测试UVR处理流程")
        print("3. 检查输出目录设置")

if __name__ == "__main__":
    main()
