#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UVR简化测试 - 快速验证最佳方案
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def test_audio_separator():
    """测试audio-separator包（最有希望的方案）"""
    print("🎯 测试 audio-separator 包...")
    
    try:
        # 检查是否已安装
        result = subprocess.run([sys.executable, "-c", "import audio_separator"], 
                              capture_output=True, text=True)
        
        if result.returncode != 0:
            print("📦 安装 audio-separator...")
            install_result = subprocess.run([
                sys.executable, "-m", "pip", "install", "audio-separator"
            ], capture_output=True, text=True, timeout=300)
            
            if install_result.returncode != 0:
                print(f"❌ 安装失败: {install_result.stderr}")
                return False
            else:
                print("✅ audio-separator 安装成功")
        
        # 测试导入
        from audio_separator.separator import Separator
        print("✅ audio-separator 导入成功")
        return True
        
    except Exception as e:
        print(f"❌ audio-separator 测试失败: {e}")
        return False

def test_demucs():
    """测试demucs作为备选"""
    print("\n🧠 测试 demucs...")
    
    try:
        # 检查是否已安装
        result = subprocess.run([sys.executable, "-c", "import demucs"], 
                              capture_output=True, text=True)
        
        if result.returncode != 0:
            print("📦 安装 demucs...")
            install_result = subprocess.run([
                sys.executable, "-m", "pip", "install", "demucs"
            ], capture_output=True, text=True, timeout=300)
            
            if install_result.returncode != 0:
                print(f"❌ 安装失败: {install_result.stderr}")
                return False
            else:
                print("✅ demucs 安装成功")
        
        print("✅ demucs 可用")
        return True
        
    except Exception as e:
        print(f"❌ demucs 测试失败: {e}")
        return False

def separate_with_audio_separator(audio_file, output_dir):
    """使用audio-separator进行分离"""
    print(f"\n🎵 使用 audio-separator 分离: {audio_file}")
    
    try:
        from audio_separator.separator import Separator
        
        output_path = Path(output_dir) / "audio_separator_result"
        output_path.mkdir(parents=True, exist_ok=True)
        
        start_time = time.time()
        
        # 创建分离器 - 使用最高质量模型
        separator = Separator(
            model_name='UVR-MDX-NET-Inst_HQ_3',
            output_dir=str(output_path),
            output_format='wav',
            normalization_threshold=0.9,
            amplification_threshold=0.6,
            mdx_segment_size=256,
            mdx_overlap=0.25
        )
        
        print("   加载模型...")
        separator.load_model()
        
        print("   执行分离...")
        primary_stem_path, secondary_stem_path = separator.separate(audio_file)
        
        elapsed = time.time() - start_time
        
        print(f"   ✅ 分离完成，耗时: {elapsed:.2f}秒")
        print(f"   人声文件: {primary_stem_path}")
        print(f"   伴奏文件: {secondary_stem_path}")
        
        # 检查文件大小
        if os.path.exists(primary_stem_path):
            size_mb = os.path.getsize(primary_stem_path) / (1024 * 1024)
            print(f"   人声文件大小: {size_mb:.2f} MB")
        
        if os.path.exists(secondary_stem_path):
            size_mb = os.path.getsize(secondary_stem_path) / (1024 * 1024)
            print(f"   伴奏文件大小: {size_mb:.2f} MB")
        
        return True, primary_stem_path, secondary_stem_path
        
    except Exception as e:
        print(f"   ❌ 分离失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def separate_with_demucs(audio_file, output_dir):
    """使用demucs进行分离"""
    print(f"\n🧠 使用 demucs 分离: {audio_file}")
    
    try:
        output_path = Path(output_dir) / "demucs_result"
        output_path.mkdir(parents=True, exist_ok=True)
        
        start_time = time.time()
        
        # 使用最新的htdemucs模型
        cmd = [
            sys.executable, "-m", "demucs.separate",
            "--model", "htdemucs",
            "--two-stems=vocals",
            "-o", str(output_path),
            str(audio_file)
        ]
        
        print(f"   执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        elapsed = time.time() - start_time
        
        if result.returncode == 0:
            print(f"   ✅ 分离完成，耗时: {elapsed:.2f}秒")
            
            # 查找输出文件
            audio_name = Path(audio_file).stem
            vocals_file = output_path / "htdemucs" / audio_name / "vocals.wav"
            no_vocals_file = output_path / "htdemucs" / audio_name / "no_vocals.wav"
            
            if vocals_file.exists():
                print(f"   人声文件: {vocals_file}")
                size_mb = vocals_file.stat().st_size / (1024 * 1024)
                print(f"   人声文件大小: {size_mb:.2f} MB")
            
            if no_vocals_file.exists():
                print(f"   伴奏文件: {no_vocals_file}")
                size_mb = no_vocals_file.stat().st_size / (1024 * 1024)
                print(f"   伴奏文件大小: {size_mb:.2f} MB")
            
            return True, str(vocals_file), str(no_vocals_file)
        else:
            print(f"   ❌ 分离失败: {result.stderr}")
            return False, None, None
            
    except Exception as e:
        print(f"   ❌ demucs异常: {e}")
        return False, None, None

def create_integration_code(successful_method):
    """生成集成代码"""
    print(f"\n📝 生成集成代码 (基于 {successful_method})...")
    
    if successful_method == "audio-separator":
        code = '''
def separate_vocals_uvr_automated(audio_file, output_dir, log_func=None):
    """
    UVR自动化人声分离 - 集成版本
    
    Args:
        audio_file: 输入音频文件路径
        output_dir: 输出目录
        log_func: 日志函数
    
    Returns:
        tuple: (success, vocals_file, accompaniment_file)
    """
    try:
        from audio_separator.separator import Separator
        import os
        from pathlib import Path
        
        if log_func:
            log_func("[UVR] 开始高质量人声分离...")
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 创建分离器 - 使用最高质量模型
        separator = Separator(
            model_name='UVR-MDX-NET-Inst_HQ_3',
            output_dir=str(output_path),
            output_format='wav',
            normalization_threshold=0.9,
            amplification_threshold=0.6,
            mdx_segment_size=256,
            mdx_overlap=0.25
        )
        
        if log_func:
            log_func("[UVR] 加载AI模型...")
        
        separator.load_model()
        
        if log_func:
            log_func("[UVR] 执行人声分离...")
        
        # 执行分离
        vocals_file, accompaniment_file = separator.separate(audio_file)
        
        if log_func:
            log_func(f"[UVR] ✅ 分离完成")
            log_func(f"[UVR]   人声: {os.path.basename(vocals_file)}")
            log_func(f"[UVR]   伴奏: {os.path.basename(accompaniment_file)}")
        
        return True, vocals_file, accompaniment_file
        
    except Exception as e:
        if log_func:
            log_func(f"[UVR] ❌ 分离失败: {e}")
        return False, None, None
'''
    
    elif successful_method == "demucs":
        code = '''
def separate_vocals_uvr_automated(audio_file, output_dir, log_func=None):
    """
    Demucs自动化人声分离 - 集成版本
    """
    try:
        import subprocess
        import sys
        from pathlib import Path
        
        if log_func:
            log_func("[Demucs] 开始高质量人声分离...")
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 使用最新的htdemucs模型
        cmd = [
            sys.executable, "-m", "demucs.separate",
            "--model", "htdemucs",
            "--two-stems=vocals",
            "-o", str(output_path),
            str(audio_file)
        ]
        
        if log_func:
            log_func("[Demucs] 执行人声分离...")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            # 查找输出文件
            audio_name = Path(audio_file).stem
            vocals_file = output_path / "htdemucs" / audio_name / "vocals.wav"
            accompaniment_file = output_path / "htdemucs" / audio_name / "no_vocals.wav"
            
            if log_func:
                log_func(f"[Demucs] ✅ 分离完成")
            
            return True, str(vocals_file), str(accompaniment_file)
        else:
            if log_func:
                log_func(f"[Demucs] ❌ 分离失败: {result.stderr}")
            return False, None, None
            
    except Exception as e:
        if log_func:
            log_func(f"[Demucs] ❌ 分离失败: {e}")
        return False, None, None
'''
    
    # 保存集成代码
    with open("uvr_integration_code.py", "w", encoding="utf-8") as f:
        f.write(code)
    
    print(f"✅ 集成代码已保存到: uvr_integration_code.py")

def main():
    """主测试函数"""
    print("🚀 UVR简化集成测试")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("用法: python test_uvr_simple.py <音频文件>")
        print("示例: python test_uvr_simple.py test.wav")
        return
    
    audio_file = sys.argv[1]
    
    if not os.path.exists(audio_file):
        print(f"❌ 音频文件不存在: {audio_file}")
        return
    
    output_dir = "uvr_simple_test"
    
    # 测试可用性
    audio_separator_ok = test_audio_separator()
    demucs_ok = test_demucs()
    
    if not audio_separator_ok and not demucs_ok:
        print("\n❌ 没有可用的分离工具")
        print("💡 请先安装: pip install audio-separator 或 pip install demucs")
        return
    
    successful_method = None
    
    # 优先测试audio-separator
    if audio_separator_ok:
        success, vocals, accompaniment = separate_with_audio_separator(audio_file, output_dir)
        if success:
            successful_method = "audio-separator"
    
    # 如果audio-separator失败，测试demucs
    if not successful_method and demucs_ok:
        success, vocals, accompaniment = separate_with_demucs(audio_file, output_dir)
        if success:
            successful_method = "demucs"
    
    # 生成结果
    if successful_method:
        print(f"\n🎉 测试成功！推荐使用: {successful_method}")
        print(f"📁 输出目录: {output_dir}")
        
        # 生成集成代码
        create_integration_code(successful_method)
        
        print("\n✅ 可以集成到正式代码中！")
        print("📋 下一步:")
        print("   1. 查看生成的 uvr_integration_code.py")
        print("   2. 将函数集成到 pseudo_original.py")
        print("   3. 替换现有的Spleeter调用")
        
    else:
        print("\n❌ 所有方法都失败了")
        print("🔧 请检查依赖安装和音频文件格式")

if __name__ == "__main__":
    main()
