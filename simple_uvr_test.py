#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的UVR测试
"""

import os
import subprocess
import sys

def test_uvr_simple():
    """简单测试UVR"""
    print("🎵 简单UVR测试")
    
    # 检查文件
    input_file = r"E:\视频处理\人声分离测试\原始音频.wav"
    output_dir = r"E:\视频处理\人声分离测试"
    uvr_exe = r"C:\Users\<USER>\AppData\Local\Programs\Ultimate Vocal Remover\UVR.exe"
    
    print(f"检查输入文件: {input_file}")
    if os.path.exists(input_file):
        print("✅ 输入文件存在")
        size_mb = os.path.getsize(input_file) / (1024 * 1024)
        print(f"文件大小: {size_mb:.2f}MB")
    else:
        print("❌ 输入文件不存在")
        return False
    
    print(f"检查UVR: {uvr_exe}")
    if os.path.exists(uvr_exe):
        print("✅ UVR存在")
    else:
        print("❌ UVR不存在")
        return False
    
    print(f"输出目录: {output_dir}")
    
    # 构建命令
    cmd = [
        uvr_exe,
        "--input", input_file,
        "--output", output_dir,
        "--model", "UVR-MDX-NET-Inst_HQ_3"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ UVR执行成功")
            print("检查输出文件...")
            
            # 列出输出目录的文件
            for file in os.listdir(output_dir):
                if file.endswith('.wav') and file != '原始音频.wav':
                    file_path = os.path.join(output_dir, file)
                    size_mb = os.path.getsize(file_path) / (1024 * 1024)
                    print(f"📄 {file}: {size_mb:.2f}MB")
            
            return True
        else:
            print("❌ UVR执行失败")
            print(f"错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

if __name__ == "__main__":
    test_uvr_simple()
