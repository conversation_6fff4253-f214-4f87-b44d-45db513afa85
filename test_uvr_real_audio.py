#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UVR真实音频测试
使用您指定的音频文件和输出目录
"""

import os
import subprocess
import sys
import time
from pathlib import Path

def separate_vocals_uvr_automated(audio_file, output_dir, log_func=None):
    """
    UVR自动化人声分离 - 最终集成版本
    """
    try:
        if log_func:
            log_func("[UVR] 🎵 开始UVR高质量人声分离...")
        
        # UVR可执行文件路径
        uvr_exe_path = r"C:\Users\<USER>\AppData\Local\Programs\Ultimate Vocal Remover\UVR.exe"
        
        # 验证UVR是否存在
        if not os.path.exists(uvr_exe_path):
            if log_func:
                log_func(f"[UVR] ❌ UVR未找到: {uvr_exe_path}")
            return False, None, None
        
        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        if log_func:
            log_func("[UVR] 🔧 配置UVR参数...")
            log_func(f"[UVR] 输入文件: {audio_file}")
            log_func(f"[UVR] 输出目录: {output_path}")
        
        # 构建UVR命令
        cmd = [
            uvr_exe_path,
            "--input", str(audio_file),
            "--output", str(output_path),
            "--model", "UVR-MDX-NET-Inst_HQ_3"  # 使用最高质量模型
        ]
        
        if log_func:
            log_func("[UVR] 🚀 执行人声分离...")
            log_func(f"[UVR] 命令: {' '.join(cmd)}")
        
        start_time = time.time()
        
        # 执行UVR分离
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1200)  # 20分钟超时
        
        elapsed = time.time() - start_time
        
        if result.returncode == 0:
            if log_func:
                log_func(f"[UVR] ✅ UVR分离完成，耗时: {elapsed:.1f}秒")
            
            # 查找输出文件
            vocals_files = list(output_path.glob("*vocals*.wav"))
            instrumental_files = list(output_path.glob("*instrumental*.wav"))
            
            # 如果没找到标准命名，尝试其他可能的命名
            if not vocals_files:
                vocals_files = list(output_path.glob("*vocal*.wav"))
            if not instrumental_files:
                instrumental_files = list(output_path.glob("*music*.wav")) + list(output_path.glob("*accompan*.wav"))
            
            # 也检查所有wav文件
            all_wav_files = list(output_path.glob("*.wav"))
            
            if log_func:
                log_func(f"[UVR] 找到 {len(all_wav_files)} 个输出文件:")
                for wav_file in all_wav_files:
                    size_mb = wav_file.stat().st_size / (1024 * 1024)
                    log_func(f"[UVR]   📄 {wav_file.name}: {size_mb:.2f}MB")
            
            vocals_file = str(vocals_files[0]) if vocals_files else None
            instrumental_file = str(instrumental_files[0]) if instrumental_files else None
            
            # 如果没有找到标准命名的文件，使用第一个和第二个文件
            if not vocals_file and len(all_wav_files) >= 1:
                vocals_file = str(all_wav_files[0])
                if log_func:
                    log_func(f"[UVR] 使用第一个文件作为人声: {Path(vocals_file).name}")
            
            if not instrumental_file and len(all_wav_files) >= 2:
                instrumental_file = str(all_wav_files[1])
                if log_func:
                    log_func(f"[UVR] 使用第二个文件作为伴奏: {Path(instrumental_file).name}")
            
            return True, vocals_file, instrumental_file
        else:
            if log_func:
                log_func(f"[UVR] ❌ UVR分离失败，耗时: {elapsed:.1f}秒")
                if result.stderr:
                    log_func(f"[UVR] 错误信息: {result.stderr}")
                if result.stdout:
                    log_func(f"[UVR] 输出信息: {result.stdout}")
            return False, None, None
            
    except subprocess.TimeoutExpired:
        if log_func:
            log_func("[UVR] ⏰ UVR分离超时（超过20分钟）")
        return False, None, None
    except Exception as e:
        if log_func:
            log_func(f"[UVR] ❌ UVR分离异常: {e}")
        return False, None, None

def test_real_audio():
    """测试真实音频文件"""
    print("🎵 UVR真实音频测试")
    print("=" * 60)
    
    # 指定的文件路径
    input_audio = r"E:\视频处理\人声分离测试\原始音频.wav"
    output_dir = r"E:\视频处理\人声分离测试"
    
    # 检查输入文件
    if not os.path.exists(input_audio):
        print(f"❌ 输入音频文件不存在: {input_audio}")
        return False
    
    # 显示文件信息
    file_size = os.path.getsize(input_audio) / (1024 * 1024)  # MB
    print(f"📁 输入文件: {input_audio}")
    print(f"📊 文件大小: {file_size:.2f}MB")
    print(f"📂 输出目录: {output_dir}")
    
    # 测试日志函数
    def test_log(message):
        print(f"{message}")
    
    print("\n🚀 开始UVR人声分离...")
    print("=" * 60)
    
    # 执行测试
    success, vocals_file, instrumental_file = separate_vocals_uvr_automated(
        audio_file=input_audio,
        output_dir=output_dir,
        log_func=test_log
    )
    
    print("\n" + "=" * 60)
    
    if success:
        print("🎉 UVR人声分离成功！")
        
        if vocals_file and os.path.exists(vocals_file):
            size_mb = os.path.getsize(vocals_file) / (1024 * 1024)
            print(f"🎵 人声文件: {vocals_file}")
            print(f"   大小: {size_mb:.2f}MB")
        
        if instrumental_file and os.path.exists(instrumental_file):
            size_mb = os.path.getsize(instrumental_file) / (1024 * 1024)
            print(f"🎼 伴奏文件: {instrumental_file}")
            print(f"   大小: {size_mb:.2f}MB")
        
        print(f"\n📂 所有输出文件保存在: {output_dir}")
        
        # 列出输出目录中的所有文件
        output_path = Path(output_dir)
        wav_files = list(output_path.glob("*.wav"))
        if wav_files:
            print("\n📄 输出文件列表:")
            for wav_file in wav_files:
                if wav_file.name != "原始音频.wav":  # 排除原始文件
                    size_mb = wav_file.stat().st_size / (1024 * 1024)
                    mod_time = wav_file.stat().st_mtime
                    import datetime
                    mod_time_str = datetime.datetime.fromtimestamp(mod_time).strftime("%Y-%m-%d %H:%M:%S")
                    print(f"   📄 {wav_file.name}: {size_mb:.2f}MB (修改时间: {mod_time_str})")
        
        print("\n✅ 测试完成！您可以播放输出文件来验证分离效果。")
        return True
    else:
        print("❌ UVR人声分离失败")
        print("🔧 请检查:")
        print("   1. UVR软件是否正确安装")
        print("   2. 输入音频文件是否有效")
        print("   3. 输出目录是否有写入权限")
        return False

def main():
    """主函数"""
    test_real_audio()

if __name__ == "__main__":
    main()
