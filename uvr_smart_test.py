#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UVR智能测试 - 合理的自动化方案
只启动一次UVR，智能监控处理结果
"""

import os
import time
import subprocess
import psutil
from pathlib import Path
from datetime import datetime

class UVRSmartTester:
    """UVR智能测试器"""
    
    def __init__(self):
        self.input_file = r"E:\视频处理\人声分离测试\原始音频.wav"
        self.output_dir = r"E:\视频处理\人声分离测试"
        self.uvr_exe = r"C:\Users\<USER>\AppData\Local\Programs\Ultimate Vocal Remover\UVR.exe"
        self.uvr_process = None
        self.initial_files = set()
        
    def check_prerequisites(self):
        """检查前置条件"""
        print("🔍 检查前置条件...")
        
        # 检查输入文件
        if not os.path.exists(self.input_file):
            print(f"❌ 输入文件不存在: {self.input_file}")
            return False
        
        file_size = os.path.getsize(self.input_file) / (1024 * 1024)
        print(f"✅ 输入文件: {os.path.basename(self.input_file)} ({file_size:.2f}MB)")
        
        # 检查UVR
        if not os.path.exists(self.uvr_exe):
            print(f"❌ UVR不存在: {self.uvr_exe}")
            return False
        
        print(f"✅ UVR路径: {self.uvr_exe}")
        
        # 检查输出目录
        if not os.path.exists(self.output_dir):
            print(f"❌ 输出目录不存在: {self.output_dir}")
            return False
        
        print(f"✅ 输出目录: {self.output_dir}")
        
        return True
    
    def check_existing_uvr_process(self):
        """检查是否已有UVR进程运行"""
        print("\n🔍 检查现有UVR进程...")
        
        uvr_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if proc.info['name'] and 'UVR' in proc.info['name']:
                    uvr_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if uvr_processes:
            print(f"⚠️ 发现 {len(uvr_processes)} 个现有UVR进程:")
            for proc in uvr_processes:
                print(f"   PID: {proc.info['pid']}, 名称: {proc.info['name']}")
            
            choice = input("\n是否关闭现有UVR进程？(y/n): ").strip().lower()
            if choice == 'y':
                for proc in uvr_processes:
                    try:
                        proc.terminate()
                        print(f"✅ 已终止进程 PID: {proc.info['pid']}")
                    except:
                        print(f"❌ 无法终止进程 PID: {proc.info['pid']}")
                time.sleep(2)
        else:
            print("✅ 没有现有UVR进程")
    
    def record_initial_state(self):
        """记录初始文件状态"""
        print("\n📋 记录初始文件状态...")
        
        self.initial_files = set()
        try:
            for file in os.listdir(self.output_dir):
                if file.endswith('.wav'):
                    self.initial_files.add(file)
            
            print(f"📁 初始WAV文件数量: {len(self.initial_files)}")
            if self.initial_files:
                print("   现有文件:")
                for file in sorted(self.initial_files):
                    print(f"     📄 {file}")
        except Exception as e:
            print(f"❌ 读取目录失败: {e}")
            return False
        
        return True
    
    def start_uvr_once(self):
        """启动一次UVR（智能方式）"""
        print("\n🚀 启动UVR...")
        
        try:
            # 启动UVR并传递音频文件
            cmd = [self.uvr_exe, self.input_file]
            print(f"执行命令: {' '.join(cmd)}")
            
            self.uvr_process = subprocess.Popen(
                cmd,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            print(f"✅ UVR已启动，进程ID: {self.uvr_process.pid}")
            
            # 等待UVR完全启动
            time.sleep(3)
            
            # 检查进程是否还在运行
            if self.uvr_process.poll() is None:
                print("✅ UVR进程运行正常")
                return True
            else:
                print("❌ UVR进程意外退出")
                return False
                
        except Exception as e:
            print(f"❌ 启动UVR失败: {e}")
            return False
    
    def show_user_instructions(self):
        """显示用户操作指导"""
        print("\n" + "=" * 60)
        print("📋 UVR操作指导")
        print("=" * 60)
        print("🎵 UVR应该已经打开并加载了您的音频文件")
        print()
        print("请在UVR界面中进行以下操作:")
        print("1. 📂 确认输入文件已加载: 原始音频.wav")
        print("2. 📁 设置输出目录: E:\\视频处理\\人声分离测试")
        print("3. 🎛️ 选择模型: UVR-MDX-NET-Inst_HQ_3 (推荐)")
        print("4. ⚙️ 检查其他设置 (通常默认即可)")
        print("5. ▶️ 点击 'Start Processing' 开始处理")
        print("6. ⏳ 等待处理完成")
        print()
        print("💡 提示:")
        print("   - 处理时间通常需要1-3分钟")
        print("   - 处理过程中请不要关闭UVR")
        print("   - 我会自动监控输出文件")
        print("=" * 60)
    
    def monitor_processing(self, max_wait_minutes=10):
        """智能监控处理过程"""
        print(f"\n🔍 开始监控处理过程 (最长等待 {max_wait_minutes} 分钟)...")
        
        max_wait_seconds = max_wait_minutes * 60
        check_interval = 10  # 每10秒检查一次
        elapsed_time = 0
        last_report_time = 0
        
        while elapsed_time < max_wait_seconds:
            time.sleep(check_interval)
            elapsed_time += check_interval
            
            # 检查UVR进程是否还在运行
            if self.uvr_process and self.uvr_process.poll() is not None:
                print("⚠️ UVR进程已退出")
                break
            
            # 检查新文件
            try:
                current_files = set()
                for file in os.listdir(self.output_dir):
                    if file.endswith('.wav'):
                        current_files.add(file)
                
                new_files = current_files - self.initial_files
                
                if new_files:
                    print(f"\n🎉 检测到新文件! (处理时间: {elapsed_time}秒)")
                    print("📄 新生成的文件:")
                    
                    for new_file in sorted(new_files):
                        file_path = os.path.join(self.output_dir, new_file)
                        size_mb = os.path.getsize(file_path) / (1024 * 1024)
                        mod_time = os.path.getmtime(file_path)
                        mod_time_str = datetime.fromtimestamp(mod_time).strftime("%H:%M:%S")
                        
                        # 判断文件类型
                        if 'vocal' in new_file.lower():
                            file_type = "🎤 人声"
                        elif 'instrumental' in new_file.lower() or 'music' in new_file.lower():
                            file_type = "🎼 伴奏"
                        else:
                            file_type = "📄 音频"
                        
                        print(f"   {file_type}: {new_file}")
                        print(f"     大小: {size_mb:.2f}MB, 生成时间: {mod_time_str}")
                    
                    print("\n✅ UVR处理完成!")
                    return True, new_files
                
                # 每分钟报告一次进度
                if elapsed_time - last_report_time >= 60:
                    minutes = elapsed_time // 60
                    print(f"⏳ 已等待 {minutes} 分钟... (UVR正在处理中)")
                    last_report_time = elapsed_time
                    
            except Exception as e:
                print(f"❌ 监控过程出错: {e}")
                break
        
        print(f"\n⏰ 监控超时 ({max_wait_minutes} 分钟)")
        return False, set()
    
    def cleanup(self):
        """清理资源"""
        print("\n🧹 清理资源...")
        
        if self.uvr_process:
            try:
                if self.uvr_process.poll() is None:
                    print("🛑 终止UVR进程...")
                    self.uvr_process.terminate()
                    time.sleep(2)
                    
                    if self.uvr_process.poll() is None:
                        print("🔨 强制终止UVR进程...")
                        self.uvr_process.kill()
                
                print("✅ UVR进程已清理")
            except Exception as e:
                print(f"⚠️ 清理UVR进程时出错: {e}")
    
    def run_test(self):
        """运行完整测试"""
        print("🎵 UVR智能测试")
        print("=" * 60)
        
        try:
            # 1. 检查前置条件
            if not self.check_prerequisites():
                return False
            
            # 2. 检查现有进程
            self.check_existing_uvr_process()
            
            # 3. 记录初始状态
            if not self.record_initial_state():
                return False
            
            # 4. 启动UVR
            if not self.start_uvr_once():
                return False
            
            # 5. 显示操作指导
            self.show_user_instructions()
            
            # 6. 等待用户确认
            input("\n按Enter键开始监控处理过程...")
            
            # 7. 监控处理
            success, new_files = self.monitor_processing()
            
            if success:
                print("\n🎯 测试结果:")
                print("✅ UVR自动化测试成功!")
                print("✅ 证明UVR可以通过以下方式自动化:")
                print("   1. 命令行启动并传递音频文件")
                print("   2. 用户在GUI中设置参数")
                print("   3. 程序监控输出文件变化")
                print("   4. 自动检测处理完成")
                
                print(f"\n📁 输出文件位置: {self.output_dir}")
                print("📋 下一步可以:")
                print("   1. 验证分离质量")
                print("   2. 设计完整的自动化流程")
                print("   3. 集成到主程序中")
                
                return True
            else:
                print("\n❌ 测试失败或超时")
                print("💡 可能的原因:")
                print("   1. UVR处理时间过长")
                print("   2. 用户未在UVR中开始处理")
                print("   3. 输出目录设置错误")
                print("   4. UVR遇到错误")
                
                return False
                
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断测试")
            return False
        except Exception as e:
            print(f"\n❌ 测试过程出错: {e}")
            return False
        finally:
            self.cleanup()

def main():
    """主函数"""
    tester = UVRSmartTester()
    success = tester.run_test()
    
    if success:
        print("\n🎉 UVR智能测试完成!")
    else:
        print("\n🔧 请检查问题并重试")

if __name__ == "__main__":
    main()
