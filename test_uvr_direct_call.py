#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接调用UVR软件的自动化方案
通过命令行调用您已安装的UVR软件
"""

import os
import sys
import subprocess
import time
from pathlib import Path
import tempfile
import shutil

class UVRDirectCaller:
    """UVR直接调用器"""
    
    def __init__(self):
        self.temp_dir = Path(tempfile.mkdtemp(prefix="uvr_direct_"))
        self.uvr_path = None
        
        print("🎵 UVR直接调用器初始化")
        print(f"临时目录: {self.temp_dir}")
        
    def find_uvr_installation(self):
        """查找UVR安装路径"""
        print("\n🔍 查找UVR安装...")
        
        # 可能的UVR安装路径（包含找到的实际路径）
        possible_paths = [
            # 实际找到的路径
            r"C:\Users\<USER>\AppData\Local\Programs\Ultimate Vocal Remover",
            # 标准安装路径
            r"C:\Program Files\Ultimate Vocal Remover",
            r"C:\Program Files (x86)\Ultimate Vocal Remover",
            # 用户目录
            os.path.expanduser("~/Ultimate Vocal Remover"),
            os.path.expanduser("~/AppData/Local/Ultimate Vocal Remover"),
            os.path.expanduser("~/AppData/Local/Programs/Ultimate Vocal Remover"),
            # 桌面和下载目录
            os.path.expanduser("~/Desktop/Ultimate Vocal Remover"),
            os.path.expanduser("~/Downloads/Ultimate Vocal Remover"),
            # 当前目录
            "./Ultimate Vocal Remover",
            "../Ultimate Vocal Remover",
            # 常见的解压位置
            r"E:\Ultimate Vocal Remover",
            r"D:\Ultimate Vocal Remover",
            r"C:\Ultimate Vocal Remover",
        ]
        
        for path in possible_paths:
            uvr_path = Path(path)
            if uvr_path.exists():
                # 查找UVR可执行文件
                exe_files = [
                    "Ultimate Vocal Remover.exe",
                    "UVR.exe",
                    "uvr.exe",
                    "main.exe"
                ]
                
                for exe_file in exe_files:
                    exe_path = uvr_path / exe_file
                    if exe_path.exists():
                        self.uvr_path = exe_path
                        print(f"✅ 找到UVR: {exe_path}")
                        return True
        
        print("❌ 未找到UVR安装")
        return False
    
    def method_1_uvr_command_line(self, audio_file, output_dir):
        """方法1: UVR命令行调用"""
        print("\n🎯 方法1: UVR命令行调用")
        
        if not self.uvr_path:
            print("   ❌ 未找到UVR安装路径")
            return False, 0, None, None
        
        try:
            output_path = Path(output_dir) / "uvr_direct"
            output_path.mkdir(parents=True, exist_ok=True)
            
            start_time = time.time()
            
            # 尝试不同的命令行参数格式
            cmd_variations = [
                # 格式1: 标准参数
                [
                    str(self.uvr_path),
                    "--input", str(audio_file),
                    "--output", str(output_path),
                    "--model", "UVR-MDX-NET-Inst_HQ_3"
                ],
                # 格式2: 简化参数
                [
                    str(self.uvr_path),
                    str(audio_file),
                    str(output_path)
                ],
                # 格式3: 批处理模式
                [
                    str(self.uvr_path),
                    "-i", str(audio_file),
                    "-o", str(output_path)
                ]
            ]
            
            for i, cmd in enumerate(cmd_variations):
                print(f"   尝试命令格式 {i+1}: {' '.join(cmd)}")
                
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                    
                    if result.returncode == 0:
                        elapsed = time.time() - start_time
                        print(f"   ✅ UVR调用成功，耗时: {elapsed:.2f}秒")
                        
                        # 查找输出文件
                        vocals_files = list(output_path.glob("*vocals*.wav"))
                        instrumental_files = list(output_path.glob("*instrumental*.wav"))
                        
                        vocals_file = vocals_files[0] if vocals_files else None
                        instrumental_file = instrumental_files[0] if instrumental_files else None
                        
                        if vocals_file:
                            print(f"   人声: {vocals_file}")
                        if instrumental_file:
                            print(f"   伴奏: {instrumental_file}")
                        
                        return True, elapsed, str(vocals_file) if vocals_file else None, str(instrumental_file) if instrumental_file else None
                    else:
                        print(f"   ❌ 格式 {i+1} 失败: {result.stderr}")
                        
                except subprocess.TimeoutExpired:
                    print(f"   ⏰ 格式 {i+1} 超时")
                except Exception as e:
                    print(f"   ❌ 格式 {i+1} 异常: {e}")
            
            print("   ❌ 所有命令格式都失败了")
            return False, 0, None, None
            
        except Exception as e:
            print(f"   ❌ UVR调用异常: {e}")
            return False, 0, None, None
    
    def method_2_uvr_batch_file(self, audio_file, output_dir):
        """方法2: 创建批处理文件调用UVR"""
        print("\n📝 方法2: 批处理文件调用")
        
        if not self.uvr_path:
            print("   ❌ 未找到UVR安装路径")
            return False, 0, None, None
        
        try:
            output_path = Path(output_dir) / "uvr_batch"
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 创建批处理文件
            batch_file = self.temp_dir / "uvr_process.bat"
            
            batch_content = f'''@echo off
cd /d "{self.uvr_path.parent}"
"{self.uvr_path}" "{audio_file}" "{output_path}"
echo UVR processing completed
'''
            
            with open(batch_file, 'w', encoding='utf-8') as f:
                f.write(batch_content)
            
            print(f"   创建批处理文件: {batch_file}")
            
            start_time = time.time()
            
            # 执行批处理文件
            result = subprocess.run([str(batch_file)], capture_output=True, text=True, timeout=300, shell=True)
            elapsed = time.time() - start_time
            
            if result.returncode == 0:
                print(f"   ✅ 批处理调用成功，耗时: {elapsed:.2f}秒")
                
                # 查找输出文件
                vocals_files = list(output_path.glob("*vocals*.wav"))
                instrumental_files = list(output_path.glob("*instrumental*.wav"))
                
                vocals_file = vocals_files[0] if vocals_files else None
                instrumental_file = instrumental_files[0] if instrumental_files else None
                
                return True, elapsed, str(vocals_file) if vocals_file else None, str(instrumental_file) if instrumental_file else None
            else:
                print(f"   ❌ 批处理调用失败: {result.stderr}")
                return False, 0, None, None
                
        except Exception as e:
            print(f"   ❌ 批处理调用异常: {e}")
            return False, 0, None, None
    
    def method_3_copy_to_uvr_folder(self, audio_file, output_dir):
        """方法3: 复制文件到UVR目录并监控输出"""
        print("\n📁 方法3: 文件复制监控")
        
        if not self.uvr_path:
            print("   ❌ 未找到UVR安装路径")
            return False, 0, None, None
        
        try:
            # 假设UVR有输入和输出文件夹
            uvr_dir = self.uvr_path.parent
            input_folder = uvr_dir / "input"
            output_folder = uvr_dir / "output"
            
            # 创建文件夹（如果不存在）
            input_folder.mkdir(exist_ok=True)
            output_folder.mkdir(exist_ok=True)
            
            # 复制音频文件到输入文件夹
            audio_name = Path(audio_file).name
            input_file = input_folder / audio_name
            shutil.copy2(audio_file, input_file)
            
            print(f"   复制音频到: {input_file}")
            print("   ⚠️ 请手动在UVR中处理文件，然后按Enter继续...")
            input("   按Enter键继续检查输出...")
            
            # 检查输出文件夹
            vocals_files = list(output_folder.glob(f"*{Path(audio_file).stem}*vocals*.wav"))
            instrumental_files = list(output_folder.glob(f"*{Path(audio_file).stem}*instrumental*.wav"))
            
            if vocals_files or instrumental_files:
                output_path = Path(output_dir) / "uvr_manual"
                output_path.mkdir(parents=True, exist_ok=True)
                
                vocals_file = None
                instrumental_file = None
                
                if vocals_files:
                    vocals_file = output_path / vocals_files[0].name
                    shutil.copy2(vocals_files[0], vocals_file)
                    print(f"   人声: {vocals_file}")
                
                if instrumental_files:
                    instrumental_file = output_path / instrumental_files[0].name
                    shutil.copy2(instrumental_files[0], instrumental_file)
                    print(f"   伴奏: {instrumental_file}")
                
                return True, 0, str(vocals_file) if vocals_file else None, str(instrumental_file) if instrumental_file else None
            else:
                print("   ❌ 未找到输出文件")
                return False, 0, None, None
                
        except Exception as e:
            print(f"   ❌ 文件复制监控异常: {e}")
            return False, 0, None, None
    
    def test_all_methods(self, audio_file, output_dir="uvr_direct_test"):
        """测试所有直接调用方法"""
        print("🚀 开始UVR直接调用测试")
        print("=" * 80)
        
        if not os.path.exists(audio_file):
            print(f"❌ 音频文件不存在: {audio_file}")
            return False
        
        # 查找UVR安装
        if not self.find_uvr_installation():
            print("❌ 无法找到UVR安装，请确保UVR已正确安装")
            return False
        
        output_base = Path(output_dir)
        output_base.mkdir(exist_ok=True)
        
        results = {}
        
        # 测试所有方法
        methods = [
            ("UVR命令行", self.method_1_uvr_command_line),
            ("批处理调用", self.method_2_uvr_batch_file),
            ("手动处理", self.method_3_copy_to_uvr_folder)
        ]
        
        successful_method = None
        
        for method_name, method_func in methods:
            try:
                success, elapsed, vocals_file, instrumental_file = method_func(audio_file, output_base)
                results[method_name] = {
                    'success': success,
                    'time': elapsed,
                    'vocals': vocals_file,
                    'instrumental': instrumental_file
                }
                
                if success and not successful_method:
                    successful_method = method_name
                    print(f"🎉 找到可用方法: {method_name}")
                    
            except Exception as e:
                print(f"❌ {method_name} 测试异常: {e}")
                results[method_name] = {
                    'success': False,
                    'time': 0,
                    'vocals': None,
                    'instrumental': None
                }
        
        # 输出测试结果
        print("\n" + "=" * 80)
        print("📊 测试结果汇总:")
        print("=" * 80)
        
        for method_name, result in results.items():
            status = "✅ 成功" if result['success'] else "❌ 失败"
            time_str = f"{result['time']:.2f}秒" if result['time'] > 0 else "N/A"
            print(f"{method_name:15} | {status:8} | 耗时: {time_str}")
        
        if successful_method:
            print(f"\n🎯 推荐使用: {successful_method}")
            print(f"📁 输出目录: {output_base}")
            
            # 生成集成代码
            self.create_integration_code(successful_method)
            return True
        else:
            print("\n❌ 所有方法都失败了")
            print("💡 建议:")
            print("   1. 确保UVR软件已正确安装")
            print("   2. 尝试手动运行UVR软件")
            print("   3. 检查UVR是否支持命令行参数")
            return False
    
    def create_integration_code(self, method_name):
        """生成集成代码"""
        print(f"\n📝 生成集成代码 (基于 {method_name})...")
        
        code = f'''
def separate_vocals_uvr_direct(audio_file, output_dir, log_func=None):
    """
    UVR直接调用人声分离 - 集成版本
    基于方法: {method_name}
    
    Args:
        audio_file: 输入音频文件路径
        output_dir: 输出目录
        log_func: 日志函数
    
    Returns:
        tuple: (success, vocals_file, instrumental_file)
    """
    try:
        import subprocess
        from pathlib import Path
        
        if log_func:
            log_func("[UVR直调] 开始UVR高质量人声分离...")
        
        # 查找UVR安装路径
        uvr_paths = [
            r"C:\\Program Files\\Ultimate Vocal Remover\\Ultimate Vocal Remover.exe",
            r"C:\\Program Files (x86)\\Ultimate Vocal Remover\\Ultimate Vocal Remover.exe",
            # 添加更多可能的路径
        ]
        
        uvr_exe = None
        for path in uvr_paths:
            if Path(path).exists():
                uvr_exe = path
                break
        
        if not uvr_exe:
            if log_func:
                log_func("[UVR直调] ❌ 未找到UVR安装")
            return False, None, None
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        if log_func:
            log_func("[UVR直调] 执行UVR分离...")
        
        # 根据成功的方法调用UVR
        cmd = [uvr_exe, str(audio_file), str(output_path)]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            # 查找输出文件
            vocals_files = list(output_path.glob("*vocals*.wav"))
            instrumental_files = list(output_path.glob("*instrumental*.wav"))
            
            vocals_file = str(vocals_files[0]) if vocals_files else None
            instrumental_file = str(instrumental_files[0]) if instrumental_files else None
            
            if log_func:
                log_func(f"[UVR直调] ✅ 分离完成")
                if vocals_file:
                    log_func(f"[UVR直调]   人声: {{Path(vocals_file).name}}")
                if instrumental_file:
                    log_func(f"[UVR直调]   伴奏: {{Path(instrumental_file).name}}")
            
            return True, vocals_file, instrumental_file
        else:
            if log_func:
                log_func(f"[UVR直调] ❌ 分离失败")
            return False, None, None
            
    except Exception as e:
        if log_func:
            log_func(f"[UVR直调] ❌ 分离失败: {{e}}")
        return False, None, None
'''
        
        # 保存集成代码
        with open("uvr_direct_integration.py", "w", encoding="utf-8") as f:
            f.write(code)
        
        print(f"✅ 集成代码已保存到: uvr_direct_integration.py")
    
    def cleanup(self):
        """清理临时文件"""
        try:
            shutil.rmtree(self.temp_dir)
            print(f"🧹 清理临时目录: {self.temp_dir}")
        except Exception as e:
            print(f"⚠️ 清理临时目录失败: {e}")

def main():
    """主测试函数"""
    print("🚀 UVR直接调用测试")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        # 使用测试音频
        audio_file = "test_audio.wav"
        if not os.path.exists(audio_file):
            print("❌ 请先运行 install_uvr_dependencies.py 生成测试音频")
            print("或者指定音频文件: python test_uvr_direct_call.py <音频文件>")
            return
    else:
        audio_file = sys.argv[1]
    
    if not os.path.exists(audio_file):
        print(f"❌ 音频文件不存在: {audio_file}")
        return
    
    # 创建测试器
    caller = UVRDirectCaller()
    
    try:
        # 执行测试
        success = caller.test_all_methods(audio_file)
        
        if success:
            print("\n🎉 UVR直接调用测试成功！")
            print("✅ 可以集成到正式代码中")
            print("📋 下一步:")
            print("   1. 查看生成的 uvr_direct_integration.py")
            print("   2. 将函数集成到 pseudo_original.py")
            print("   3. 替换现有的Spleeter调用")
        else:
            print("\n❌ UVR直接调用测试失败")
            print("🔧 请检查UVR安装和配置")
            
    finally:
        # 清理资源
        caller.cleanup()

if __name__ == "__main__":
    main()
