# 🎵 UVR自动化集成测试指南

## 📋 概述

这套测试脚本用于验证UVR (Ultimate Vocal Remover) 的完全自动化集成，**无需手动打开UVR软件**，实现后台自动人声分离。

## 🚀 快速开始

### 第1步：安装依赖
```bash
python install_uvr_dependencies.py
```

### 第2步：简单测试
```bash
python test_uvr_simple.py test_audio.wav
```

### 第3步：完整测试（可选）
```bash
python test_uvr_integration.py test_audio.wav
```

## 📁 文件说明

### 🔧 核心测试文件

| 文件名 | 用途 | 推荐使用 |
|--------|------|----------|
| `install_uvr_dependencies.py` | 自动安装所有必要依赖 | ⭐⭐⭐ 必须先运行 |
| `test_uvr_simple.py` | 快速测试最佳方案 | ⭐⭐⭐ 主要测试 |
| `test_uvr_integration.py` | 完整的集成测试 | ⭐⭐ 详细测试 |

### 📄 输出文件

| 文件名 | 内容 | 用途 |
|--------|------|------|
| `uvr_integration_code.py` | 生成的集成代码 | 复制到正式代码中 |
| `test_audio.wav` | 自动生成的测试音频 | 用于测试分离效果 |

## 🎯 测试方案

### 方案A：audio-separator（推荐）⭐⭐⭐⭐⭐
- **优点**：UVR同等质量，完全自动化
- **缺点**：需要安装额外包
- **模型**：UVR-MDX-NET-Inst_HQ_3（最高质量）

### 方案B：demucs（备选）⭐⭐⭐⭐
- **优点**：Facebook AI，开源稳定
- **缺点**：质量略低于UVR
- **模型**：htdemucs（最新版本）

### 方案C：直接调用UVR（实验性）⭐⭐⭐
- **优点**：原生UVR质量
- **缺点**：需要找到UVR安装路径

## 📊 测试流程

```mermaid
graph TD
    A[运行安装脚本] --> B[检查Python版本]
    B --> C[安装基础依赖]
    C --> D[安装audio-separator]
    D --> E[安装demucs备选]
    E --> F[测试导入]
    F --> G[创建测试音频]
    G --> H[运行简单测试]
    H --> I{测试成功?}
    I -->|是| J[生成集成代码]
    I -->|否| K[检查错误日志]
    J --> L[集成到正式代码]
```

## 🔍 预期结果

### ✅ 成功情况
```
🎉 测试成功！推荐使用: audio-separator
📁 输出目录: uvr_simple_test
✅ 可以集成到正式代码中！
```

### ❌ 失败情况
```
❌ 所有方法都失败了
🔧 请检查依赖安装和音频文件格式
```

## 🛠️ 故障排除

### 问题1：audio-separator安装失败
```bash
# 解决方案1：强制重新安装
pip install audio-separator --force-reinstall

# 解决方案2：从GitHub安装
pip install git+https://github.com/karaokenerds/python-audio-separator.git

# 解决方案3：使用conda
conda install -c conda-forge audio-separator
```

### 问题2：torch相关错误
```bash
# 卸载重装torch
pip uninstall torch torchaudio
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu
```

### 问题3：内存不足
```bash
# 在测试脚本中降低质量设置
# 修改 mdx_segment_size 从 256 到 128
```

## 🎵 音频格式支持

### ✅ 支持的格式
- WAV (推荐)
- MP3
- FLAC
- M4A
- OGG

### ⚠️ 注意事项
- 建议使用WAV格式获得最佳质量
- 文件大小建议不超过100MB
- 采样率建议44.1kHz或48kHz

## 🔗 集成到正式代码

### 第1步：复制生成的函数
从 `uvr_integration_code.py` 复制 `separate_vocals_uvr_automated` 函数

### 第2步：修改 pseudo_original.py
找到现有的Spleeter调用，替换为：
```python
# 替换原有的Spleeter调用
success, vocals_file, accompaniment_file = separate_vocals_uvr_automated(
    audio_file=temp_audio,
    output_dir=spleeter_output_dir,
    log_func=log_func
)
```

### 第3步：测试集成
在正式环境中测试新的人声分离功能

## 📈 性能对比

| 方案 | 质量 | 速度 | 内存占用 | 推荐度 |
|------|------|------|----------|--------|
| audio-separator | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| demucs | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 原Spleeter | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

## 🎯 下一步计划

1. **测试验证**：使用提供的脚本验证集成效果
2. **性能优化**：根据测试结果调整参数
3. **正式集成**：将成功的方案集成到主程序
4. **用户反馈**：收集实际使用效果反馈
5. **持续改进**：根据反馈继续优化

## 📞 支持

如果遇到问题：
1. 查看控制台错误信息
2. 检查 `install_uvr_dependencies.py` 的输出
3. 确认音频文件格式正确
4. 尝试使用生成的 `test_audio.wav` 进行测试

---

**🎉 预期效果：用户在您的软件中点击人声分离，后台自动调用UVR级别的AI模型，无需任何手动操作，直接获得高质量的分离结果！**
