#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UVR完全自动化测试
研究所有可能的自动化方法
"""

import os
import subprocess
import sys
import time
from pathlib import Path

class UVRFullAutomationTester:
    """UVR完全自动化测试器"""
    
    def __init__(self):
        self.input_file = r"E:\视频处理\人声分离测试\原始音频.wav"
        self.output_dir = r"E:\视频处理\人声分离测试"
        self.uvr_exe = r"C:\Users\<USER>\AppData\Local\Programs\Ultimate Vocal Remover\UVR.exe"
        self.uvr_launcher = r"C:\Users\<USER>\AppData\Local\Programs\Ultimate Vocal Remover\UVR_Launcher.exe"
        
    def test_method_1_direct_parameters(self):
        """方法1: 直接参数调用"""
        print("🎯 方法1: 直接参数调用")
        
        # 尝试各种可能的参数组合
        param_combinations = [
            # 标准参数
            ["--input", self.input_file, "--output", self.output_dir],
            ["-i", self.input_file, "-o", self.output_dir],
            ["--source", self.input_file, "--dest", self.output_dir],
            
            # 带模型参数
            ["--input", self.input_file, "--output", self.output_dir, "--model", "UVR-MDX-NET-Inst_HQ_3"],
            ["-i", self.input_file, "-o", self.output_dir, "-m", "UVR-MDX-NET-Inst_HQ_3"],
            
            # 批处理模式
            ["--batch", "--input", self.input_file, "--output", self.output_dir],
            ["--cli", "--input", self.input_file, "--output", self.output_dir],
            ["--headless", "--input", self.input_file, "--output", self.output_dir],
            
            # 简单格式
            [self.input_file, self.output_dir],
            [self.input_file],
        ]
        
        for i, params in enumerate(param_combinations):
            print(f"\n   尝试参数组合 {i+1}: {' '.join(params)}")
            
            try:
                cmd = [self.uvr_exe] + params
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    print(f"   ✅ 参数组合 {i+1} 成功!")
                    print(f"   输出: {result.stdout}")
                    return True, params
                else:
                    print(f"   ❌ 参数组合 {i+1} 失败: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"   ⏰ 参数组合 {i+1} 超时")
            except Exception as e:
                print(f"   ❌ 参数组合 {i+1} 异常: {e}")
        
        return False, None
    
    def test_method_2_launcher_exe(self):
        """方法2: 使用UVR_Launcher.exe"""
        print("\n🚀 方法2: UVR_Launcher.exe")
        
        if not os.path.exists(self.uvr_launcher):
            print("   ❌ UVR_Launcher.exe 不存在")
            return False, None
        
        param_combinations = [
            ["--input", self.input_file, "--output", self.output_dir],
            ["-i", self.input_file, "-o", self.output_dir],
            ["--batch", self.input_file, self.output_dir],
            [self.input_file, self.output_dir],
            [self.input_file],
        ]
        
        for i, params in enumerate(param_combinations):
            print(f"\n   尝试Launcher参数 {i+1}: {' '.join(params)}")
            
            try:
                cmd = [self.uvr_launcher] + params
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    print(f"   ✅ Launcher参数 {i+1} 成功!")
                    return True, params
                else:
                    print(f"   ❌ Launcher参数 {i+1} 失败: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"   ⏰ Launcher参数 {i+1} 超时")
            except Exception as e:
                print(f"   ❌ Launcher参数 {i+1} 异常: {e}")
        
        return False, None
    
    def test_method_3_python_direct(self):
        """方法3: 直接调用Python脚本"""
        print("\n🐍 方法3: 直接Python调用")
        
        # 查找可能的Python脚本
        uvr_dir = Path(self.uvr_exe).parent
        possible_scripts = [
            uvr_dir / "UVR.py",
            uvr_dir / "main.py",
            uvr_dir / "separate.py",
            uvr_dir / "cli.py",
            uvr_dir / "gui_data" / "UVR.py",
            uvr_dir / "lib_v5" / "separate.py",
        ]
        
        for script_path in possible_scripts:
            if script_path.exists():
                print(f"   找到Python脚本: {script_path}")
                
                try:
                    cmd = [sys.executable, str(script_path), "--help"]
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    
                    if result.returncode == 0 or "usage" in result.stdout.lower():
                        print(f"   ✅ 脚本支持命令行: {script_path}")
                        print(f"   帮助信息: {result.stdout}")
                        
                        # 尝试实际分离
                        sep_cmd = [sys.executable, str(script_path), 
                                 "--input", self.input_file, "--output", self.output_dir]
                        print(f"   尝试分离命令: {' '.join(sep_cmd)}")
                        
                        sep_result = subprocess.run(sep_cmd, capture_output=True, text=True, timeout=300)
                        
                        if sep_result.returncode == 0:
                            print(f"   ✅ Python脚本分离成功!")
                            return True, str(script_path)
                        else:
                            print(f"   ❌ Python脚本分离失败: {sep_result.stderr}")
                    
                except Exception as e:
                    print(f"   ❌ 测试脚本 {script_path} 失败: {e}")
        
        return False, None
    
    def test_method_4_environment_automation(self):
        """方法4: 环境变量自动化"""
        print("\n🌍 方法4: 环境变量自动化")
        
        # 设置可能的环境变量
        env_vars = {
            'UVR_INPUT': self.input_file,
            'UVR_OUTPUT': self.output_dir,
            'UVR_MODEL': 'UVR-MDX-NET-Inst_HQ_3',
            'UVR_AUTO': '1',
            'UVR_BATCH': '1',
            'UVR_CLI': '1',
            'UVR_HEADLESS': '1',
        }
        
        # 创建新的环境
        new_env = os.environ.copy()
        new_env.update(env_vars)
        
        try:
            cmd = [self.uvr_exe]
            print(f"   使用环境变量启动: {' '.join(cmd)}")
            print(f"   环境变量: {env_vars}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  timeout=300, env=new_env)
            
            if result.returncode == 0:
                print("   ✅ 环境变量方法成功!")
                return True, env_vars
            else:
                print(f"   ❌ 环境变量方法失败: {result.stderr}")
                
        except Exception as e:
            print(f"   ❌ 环境变量方法异常: {e}")
        
        return False, None
    
    def test_method_5_config_file_automation(self):
        """方法5: 配置文件自动化"""
        print("\n📄 方法5: 配置文件自动化")
        
        uvr_dir = Path(self.uvr_exe).parent
        
        # 尝试创建配置文件
        config_files = [
            uvr_dir / "config.json",
            uvr_dir / "settings.json",
            uvr_dir / "batch.json",
            uvr_dir / "auto.json",
            uvr_dir / "gui_data" / "settings.json",
        ]
        
        config_content = {
            "input_file": self.input_file,
            "output_dir": self.output_dir,
            "model": "UVR-MDX-NET-Inst_HQ_3",
            "auto_process": True,
            "batch_mode": True,
            "headless": True
        }
        
        import json
        
        for config_file in config_files:
            try:
                # 创建配置文件
                config_file.parent.mkdir(exist_ok=True)
                with open(config_file, 'w') as f:
                    json.dump(config_content, f, indent=2)
                
                print(f"   创建配置文件: {config_file}")
                
                # 尝试使用配置文件启动
                cmd = [self.uvr_exe, "--config", str(config_file)]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    print(f"   ✅ 配置文件方法成功: {config_file}")
                    return True, str(config_file)
                else:
                    print(f"   ❌ 配置文件方法失败: {result.stderr}")
                
                # 清理配置文件
                config_file.unlink()
                
            except Exception as e:
                print(f"   ❌ 配置文件 {config_file} 异常: {e}")
        
        return False, None
    
    def check_output_files(self):
        """检查是否生成了输出文件"""
        print("\n🔍 检查输出文件...")
        
        try:
            wav_files = []
            for file in os.listdir(self.output_dir):
                if file.endswith('.wav') and file != '原始音频.wav':
                    wav_files.append(file)
            
            if wav_files:
                print(f"✅ 找到 {len(wav_files)} 个输出文件:")
                for file in wav_files:
                    file_path = os.path.join(self.output_dir, file)
                    size_mb = os.path.getsize(file_path) / (1024 * 1024)
                    print(f"   📄 {file}: {size_mb:.2f}MB")
                return True, wav_files
            else:
                print("❌ 未找到输出文件")
                return False, []
                
        except Exception as e:
            print(f"❌ 检查输出文件失败: {e}")
            return False, []
    
    def run_full_test(self):
        """运行完整的自动化测试"""
        print("🚀 UVR完全自动化测试")
        print("=" * 60)
        
        # 检查前置条件
        if not os.path.exists(self.input_file):
            print(f"❌ 输入文件不存在: {self.input_file}")
            return False
        
        if not os.path.exists(self.uvr_exe):
            print(f"❌ UVR不存在: {self.uvr_exe}")
            return False
        
        print(f"✅ 输入文件: {self.input_file}")
        print(f"✅ UVR路径: {self.uvr_exe}")
        print(f"✅ 输出目录: {self.output_dir}")
        
        # 记录初始文件
        initial_files = set()
        try:
            for file in os.listdir(self.output_dir):
                if file.endswith('.wav'):
                    initial_files.add(file)
        except:
            pass
        
        print(f"📁 初始文件数量: {len(initial_files)}")
        
        # 测试所有方法
        methods = [
            ("直接参数调用", self.test_method_1_direct_parameters),
            ("UVR_Launcher", self.test_method_2_launcher_exe),
            ("Python脚本", self.test_method_3_python_direct),
            ("环境变量", self.test_method_4_environment_automation),
            ("配置文件", self.test_method_5_config_file_automation),
        ]
        
        successful_method = None
        
        for method_name, method_func in methods:
            print(f"\n{'='*60}")
            print(f"测试方法: {method_name}")
            print(f"{'='*60}")
            
            try:
                success, result = method_func()
                
                if success:
                    print(f"\n🎉 方法 '{method_name}' 成功!")
                    successful_method = (method_name, result)
                    
                    # 检查输出文件
                    time.sleep(2)  # 等待文件生成
                    has_output, output_files = self.check_output_files()
                    
                    if has_output:
                        print(f"✅ 确认生成了输出文件!")
                        break
                    else:
                        print(f"⚠️ 方法成功但未生成输出文件，继续测试其他方法...")
                        successful_method = None
                else:
                    print(f"❌ 方法 '{method_name}' 失败")
                    
            except Exception as e:
                print(f"❌ 方法 '{method_name}' 异常: {e}")
        
        # 最终结果
        print(f"\n{'='*60}")
        print("🏁 测试结果")
        print(f"{'='*60}")
        
        if successful_method:
            method_name, result = successful_method
            print(f"🎉 找到可用的自动化方法: {method_name}")
            print(f"📋 方法详情: {result}")
            print(f"✅ UVR完全自动化测试成功!")
            
            # 生成集成代码
            self.generate_integration_code(method_name, result)
            
            return True
        else:
            print("❌ 所有自动化方法都失败了")
            print("💡 可能的原因:")
            print("   1. UVR版本不支持命令行自动化")
            print("   2. 需要特殊的参数格式")
            print("   3. 需要额外的配置文件")
            print("   4. UVR只支持GUI模式")
            
            print("\n🔄 建议的替代方案:")
            print("   1. 使用Demucs (已安装)")
            print("   2. 使用优化的Spleeter")
            print("   3. 研究UVR的源代码")
            
            return False
    
    def generate_integration_code(self, method_name, result):
        """生成集成代码"""
        print(f"\n📝 生成集成代码...")
        
        code = f'''
def separate_vocals_uvr_automated(audio_file, output_dir, log_func=None):
    """
    UVR完全自动化人声分离
    基于测试成功的方法: {method_name}
    
    Args:
        audio_file: 输入音频文件路径
        output_dir: 输出目录
        log_func: 日志函数
    
    Returns:
        tuple: (success, vocals_file, accompaniment_file)
    """
    import subprocess
    import os
    from pathlib import Path
    
    try:
        if log_func:
            log_func("[UVR] 🎵 开始UVR完全自动化人声分离...")
        
        uvr_exe = r"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ultimate Vocal Remover\\UVR.exe"
        
        if not os.path.exists(uvr_exe):
            if log_func:
                log_func("[UVR] ❌ UVR未找到")
            return False, None, None
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 基于成功的测试方法: {method_name}
        # 测试结果: {result}
        
        if log_func:
            log_func("[UVR] 🚀 执行自动化分离...")
        
        # 这里需要根据成功的方法实现具体的调用
        # TODO: 实现具体的自动化调用逻辑
        
        if log_func:
            log_func("[UVR] ✅ 分离完成")
        
        return True, vocals_file, accompaniment_file
        
    except Exception as e:
        if log_func:
            log_func(f"[UVR] ❌ 分离失败: {{e}}")
        return False, None, None
'''
        
        with open("uvr_final_integration.py", "w", encoding="utf-8") as f:
            f.write(code)
        
        print(f"✅ 集成代码已保存到: uvr_final_integration.py")

def main():
    """主函数"""
    tester = UVRFullAutomationTester()
    success = tester.run_full_test()
    
    if not success:
        print("\n🔄 建议使用Demucs作为替代方案")
        print("Demucs已安装且支持完全自动化")

if __name__ == "__main__":
    main()
