#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找UVR安装路径的工具
"""

import os
import sys
import subprocess
from pathlib import Path

def find_running_uvr():
    """查找正在运行的UVR进程"""
    print("🔍 查找正在运行的UVR进程...")
    
    try:
        # 使用tasklist查找UVR进程
        result = subprocess.run(['tasklist', '/fi', 'imagename eq *vocal*'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and result.stdout:
            print("找到的进程:")
            print(result.stdout)
        
        # 查找UVR相关进程
        result2 = subprocess.run(['tasklist', '/fi', 'imagename eq *uvr*'], 
                               capture_output=True, text=True, timeout=10)
        
        if result2.returncode == 0 and result2.stdout:
            print("UVR相关进程:")
            print(result2.stdout)
            
        # 查找Ultimate相关进程
        result3 = subprocess.run(['tasklist', '/fi', 'imagename eq *ultimate*'], 
                               capture_output=True, text=True, timeout=10)
        
        if result3.returncode == 0 and result3.stdout:
            print("Ultimate相关进程:")
            print(result3.stdout)
            
    except Exception as e:
        print(f"查找进程失败: {e}")

def search_uvr_files():
    """在常见位置搜索UVR文件"""
    print("\n📁 搜索UVR文件...")
    
    # 搜索路径
    search_paths = [
        "C:\\",
        "D:\\",
        "E:\\",
        os.path.expanduser("~"),
        os.path.expanduser("~/Desktop"),
        os.path.expanduser("~/Downloads"),
        os.path.expanduser("~/Documents"),
    ]
    
    # 搜索的文件名模式
    search_patterns = [
        "*Ultimate*Vocal*Remover*",
        "*UVR*",
        "*vocal*remover*",
        "*ultimate*vocal*"
    ]
    
    found_paths = []
    
    for search_path in search_paths:
        try:
            base_path = Path(search_path)
            if not base_path.exists():
                continue
                
            print(f"搜索: {base_path}")
            
            for pattern in search_patterns:
                try:
                    # 搜索文件夹
                    for path in base_path.glob(pattern):
                        if path.is_dir():
                            print(f"  找到文件夹: {path}")
                            found_paths.append(path)
                            
                            # 在文件夹中查找可执行文件
                            for exe_pattern in ["*.exe", "*.py"]:
                                for exe_file in path.glob(exe_pattern):
                                    if "vocal" in exe_file.name.lower() or "uvr" in exe_file.name.lower():
                                        print(f"    可执行文件: {exe_file}")
                                        
                except Exception as e:
                    continue
                    
        except Exception as e:
            print(f"搜索 {search_path} 失败: {e}")
            continue
    
    return found_paths

def check_wmic_processes():
    """使用WMIC查找进程路径"""
    print("\n🔍 使用WMIC查找UVR进程路径...")
    
    try:
        # 查找所有Python进程
        result = subprocess.run([
            'wmic', 'process', 'where', 'name like "%python%"', 
            'get', 'processid,executablepath,commandline'
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if 'vocal' in line.lower() or 'uvr' in line.lower():
                    print(f"找到UVR相关进程: {line}")
                    
        # 查找所有exe进程
        result2 = subprocess.run([
            'wmic', 'process', 'where', 'name like "%vocal%" or name like "%uvr%" or name like "%ultimate%"', 
            'get', 'processid,executablepath,commandline'
        ], capture_output=True, text=True, timeout=15)
        
        if result2.returncode == 0:
            lines = result2.stdout.strip().split('\n')
            for line in lines:
                if line.strip():
                    print(f"找到相关进程: {line}")
                    
    except Exception as e:
        print(f"WMIC查找失败: {e}")

def manual_path_input():
    """手动输入UVR路径"""
    print("\n✋ 手动指定UVR路径")
    print("请输入您的UVR安装路径（可执行文件的完整路径）:")
    print("例如: C:\\Ultimate Vocal Remover\\Ultimate Vocal Remover.exe")
    
    while True:
        user_path = input("UVR路径: ").strip()
        if not user_path:
            print("❌ 路径不能为空")
            continue
            
        path = Path(user_path)
        if path.exists():
            print(f"✅ 找到UVR: {path}")
            return str(path)
        else:
            print(f"❌ 路径不存在: {path}")
            retry = input("是否重新输入？(y/n): ").strip().lower()
            if retry != 'y':
                break
    
    return None

def main():
    """主函数"""
    print("🔍 UVR路径查找工具")
    print("=" * 50)
    
    # 方法1: 查找正在运行的进程
    find_running_uvr()
    
    # 方法2: 搜索文件系统
    found_paths = search_uvr_files()
    
    # 方法3: 使用WMIC
    check_wmic_processes()
    
    # 方法4: 手动输入
    if not found_paths:
        print("\n❌ 自动搜索未找到UVR")
        manual_path = manual_path_input()
        if manual_path:
            found_paths.append(Path(manual_path))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 搜索结果总结:")
    
    if found_paths:
        print("✅ 找到以下可能的UVR路径:")
        for i, path in enumerate(found_paths, 1):
            print(f"  {i}. {path}")
        
        # 保存路径到文件
        with open("uvr_paths.txt", "w", encoding="utf-8") as f:
            for path in found_paths:
                f.write(f"{path}\n")
        
        print(f"\n💾 路径已保存到: uvr_paths.txt")
        print("📋 下一步:")
        print("   1. 验证哪个路径是正确的UVR可执行文件")
        print("   2. 修改 test_uvr_direct_call.py 中的路径")
        print("   3. 重新运行测试")
        
    else:
        print("❌ 未找到UVR安装")
        print("💡 建议:")
        print("   1. 确保UVR软件已安装")
        print("   2. 记住UVR的安装位置")
        print("   3. 手动运行此脚本并输入正确路径")

if __name__ == "__main__":
    main()
