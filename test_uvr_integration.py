#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UVR (Ultimate Vocal Remover) 完全自动化集成测试
测试无GUI模式的人声分离功能
"""

import os
import sys
import time
import subprocess
import shutil
from pathlib import Path
import tempfile

class UVRAutomatedSeparator:
    """UVR自动化人声分离器"""
    
    def __init__(self):
        self.temp_dir = Path(tempfile.mkdtemp(prefix="uvr_test_"))
        self.uvr_path = None
        self.python_path = sys.executable
        
        print("🎵 UVR自动化分离器初始化")
        print(f"临时目录: {self.temp_dir}")
        
    def find_uvr_installation(self):
        """查找UVR安装路径"""
        print("\n🔍 查找UVR安装...")
        
        # 可能的UVR安装路径
        possible_paths = [
            # 标准安装路径
            r"C:\Program Files\Ultimate Vocal Remover",
            r"C:\Program Files (x86)\Ultimate Vocal Remover",
            # 用户目录
            os.path.expanduser("~/Ultimate Vocal Remover"),
            os.path.expanduser("~/AppData/Local/Ultimate Vocal Remover"),
            # 当前目录及子目录
            "./Ultimate Vocal Remover",
            "./UVR",
            "../Ultimate Vocal Remover",
            # 常见下载位置
            os.path.expanduser("~/Downloads/Ultimate Vocal Remover"),
            os.path.expanduser("~/Desktop/Ultimate Vocal Remover"),
        ]
        
        for path in possible_paths:
            uvr_path = Path(path)
            if uvr_path.exists():
                # 查找主要的Python文件
                main_files = [
                    "UVR.py",
                    "main.py", 
                    "gui_data/UVR.py",
                    "src/UVR.py"
                ]
                
                for main_file in main_files:
                    main_path = uvr_path / main_file
                    if main_path.exists():
                        self.uvr_path = uvr_path
                        print(f"✅ 找到UVR安装: {uvr_path}")
                        print(f"   主文件: {main_path}")
                        return True
        
        print("❌ 未找到UVR安装")
        return False
    
    def check_uvr_python_package(self):
        """检查是否可以作为Python包导入UVR"""
        print("\n🐍 检查UVR Python包...")
        
        try:
            # 尝试导入UVR相关模块
            import importlib.util
            
            # 常见的UVR模块名
            uvr_modules = [
                "audio_separator",
                "uvr5",
                "ultimate_vocal_remover",
                "separator"
            ]
            
            for module_name in uvr_modules:
                try:
                    module = importlib.import_module(module_name)
                    print(f"✅ 找到UVR模块: {module_name}")
                    return True, module_name
                except ImportError:
                    continue
            
            print("❌ 未找到UVR Python模块")
            return False, None
            
        except Exception as e:
            print(f"❌ 检查UVR模块时出错: {e}")
            return False, None
    
    def install_uvr_package(self):
        """尝试安装UVR相关的Python包"""
        print("\n📦 尝试安装UVR相关包...")
        
        # UVR相关的可安装包
        packages = [
            "audio-separator",  # 流行的音频分离包
            "ultimate-vocal-remover",
            "uvr5",
            "demucs",  # 备选方案
        ]
        
        for package in packages:
            try:
                print(f"   安装 {package}...")
                result = subprocess.run([
                    self.python_path, "-m", "pip", "install", package
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    print(f"   ✅ {package} 安装成功")
                    return True, package
                else:
                    print(f"   ❌ {package} 安装失败: {result.stderr}")
                    
            except Exception as e:
                print(f"   ❌ 安装 {package} 时出错: {e}")
        
        return False, None
    
    def method_1_audio_separator_package(self, audio_file, output_dir):
        """方法1: 使用audio-separator包"""
        print("\n🎯 方法1: audio-separator包")
        
        try:
            # 尝试导入audio-separator
            from audio_separator.separator import Separator
            
            output_path = Path(output_dir) / "audio_separator"
            output_path.mkdir(parents=True, exist_ok=True)
            
            start_time = time.time()
            
            # 创建分离器
            separator = Separator(
                model_name='UVR-MDX-NET-Inst_HQ_3',  # 使用高质量模型
                output_dir=str(output_path),
                output_format='wav'
            )
            
            # 执行分离
            separator.load_model()
            primary_stem_path, secondary_stem_path = separator.separate(audio_file)
            
            elapsed = time.time() - start_time
            
            print(f"   ✅ 分离成功，耗时: {elapsed:.2f}秒")
            print(f"   人声: {primary_stem_path}")
            print(f"   伴奏: {secondary_stem_path}")
            
            return True, elapsed, output_path
            
        except ImportError:
            print("   ❌ audio-separator包未安装")
            return False, 0, None
        except Exception as e:
            print(f"   ❌ 分离失败: {e}")
            return False, 0, None
    
    def method_2_uvr_command_line(self, audio_file, output_dir):
        """方法2: UVR命令行模式"""
        print("\n🖥️ 方法2: UVR命令行")
        
        if not self.uvr_path:
            print("   ❌ 未找到UVR安装路径")
            return False, 0, None
        
        try:
            output_path = Path(output_dir) / "uvr_cli"
            output_path.mkdir(parents=True, exist_ok=True)
            
            start_time = time.time()
            
            # 查找UVR的命令行脚本
            cli_scripts = [
                self.uvr_path / "UVR.py",
                self.uvr_path / "main.py",
                self.uvr_path / "cli.py",
                self.uvr_path / "separate.py"
            ]
            
            uvr_script = None
            for script in cli_scripts:
                if script.exists():
                    uvr_script = script
                    break
            
            if not uvr_script:
                print("   ❌ 未找到UVR执行脚本")
                return False, 0, None
            
            # 构建命令行参数
            cmd = [
                self.python_path,
                str(uvr_script),
                "--input", str(audio_file),
                "--output", str(output_path),
                "--model", "UVR-MDX-NET-Inst_HQ_3",
                "--format", "wav"
            ]
            
            print(f"   执行命令: {' '.join(cmd)}")
            
            # 执行分离
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            elapsed = time.time() - start_time
            
            if result.returncode == 0:
                print(f"   ✅ UVR分离成功，耗时: {elapsed:.2f}秒")
                return True, elapsed, output_path
            else:
                print(f"   ❌ UVR分离失败: {result.stderr}")
                return False, 0, None
                
        except Exception as e:
            print(f"   ❌ UVR命令行异常: {e}")
            return False, 0, None
    
    def method_3_demucs_fallback(self, audio_file, output_dir):
        """方法3: Demucs作为备选方案"""
        print("\n🧠 方法3: Demucs备选")
        
        try:
            output_path = Path(output_dir) / "demucs"
            output_path.mkdir(parents=True, exist_ok=True)
            
            start_time = time.time()
            
            # 使用最新的htdemucs模型
            cmd = [
                self.python_path, "-m", "demucs.separate",
                "--model", "htdemucs",
                "--two-stems=vocals",
                "-o", str(output_path),
                str(audio_file)
            ]
            
            print(f"   执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            elapsed = time.time() - start_time
            
            if result.returncode == 0:
                print(f"   ✅ Demucs分离成功，耗时: {elapsed:.2f}秒")
                return True, elapsed, output_path
            else:
                print(f"   ❌ Demucs分离失败: {result.stderr}")
                return False, 0, None
                
        except Exception as e:
            print(f"   ❌ Demucs异常: {e}")
            return False, 0, None
    
    def test_all_methods(self, audio_file, output_dir="uvr_test_output"):
        """测试所有自动化方法"""
        print("🚀 开始UVR自动化集成测试")
        print("=" * 80)
        
        if not os.path.exists(audio_file):
            print(f"❌ 音频文件不存在: {audio_file}")
            return False
        
        output_base = Path(output_dir)
        output_base.mkdir(exist_ok=True)
        
        results = {}
        
        # 测试所有方法
        methods = [
            ("audio-separator包", self.method_1_audio_separator_package),
            ("UVR命令行", self.method_2_uvr_command_line),
            ("Demucs备选", self.method_3_demucs_fallback)
        ]
        
        successful_method = None
        
        for method_name, method_func in methods:
            try:
                success, elapsed, output_path = method_func(audio_file, output_base)
                results[method_name] = {
                    'success': success,
                    'time': elapsed,
                    'output': output_path
                }
                
                if success and not successful_method:
                    successful_method = method_name
                    print(f"🎉 找到可用方法: {method_name}")
                    
            except Exception as e:
                print(f"❌ {method_name} 测试异常: {e}")
                results[method_name] = {
                    'success': False,
                    'time': 0,
                    'output': None
                }
        
        # 输出测试结果
        print("\n" + "=" * 80)
        print("📊 测试结果汇总:")
        print("=" * 80)
        
        for method_name, result in results.items():
            status = "✅ 成功" if result['success'] else "❌ 失败"
            time_str = f"{result['time']:.2f}秒" if result['time'] > 0 else "N/A"
            print(f"{method_name:20} | {status:8} | 耗时: {time_str}")
        
        if successful_method:
            print(f"\n🎯 推荐使用: {successful_method}")
            print(f"📁 输出目录: {output_base}")
            return True
        else:
            print("\n❌ 所有方法都失败了")
            print("💡 建议:")
            print("   1. 安装 audio-separator: pip install audio-separator")
            print("   2. 或安装 demucs: pip install demucs")
            print("   3. 或下载UVR软件到标准位置")
            return False
    
    def cleanup(self):
        """清理临时文件"""
        try:
            shutil.rmtree(self.temp_dir)
            print(f"🧹 清理临时目录: {self.temp_dir}")
        except Exception as e:
            print(f"⚠️ 清理临时目录失败: {e}")

def main():
    """主测试函数"""
    if len(sys.argv) < 2:
        print("用法: python test_uvr_integration.py <音频文件>")
        print("示例: python test_uvr_integration.py test.wav")
        return
    
    audio_file = sys.argv[1]
    
    # 创建测试器
    separator = UVRAutomatedSeparator()
    
    try:
        # 查找UVR安装
        separator.find_uvr_installation()
        
        # 检查Python包
        separator.check_uvr_python_package()
        
        # 如果需要，尝试安装包
        # separator.install_uvr_package()
        
        # 执行测试
        success = separator.test_all_methods(audio_file)
        
        if success:
            print("\n🎉 UVR自动化集成测试成功！")
            print("✅ 可以集成到正式代码中")
        else:
            print("\n❌ UVR自动化集成测试失败")
            print("🔧 需要先解决依赖问题")
            
    finally:
        # 清理资源
        separator.cleanup()

if __name__ == "__main__":
    main()
